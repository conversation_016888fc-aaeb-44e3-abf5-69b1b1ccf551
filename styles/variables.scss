
$light-green-color: #278143;
$green-color: #28a745;
$light-green-50: #DFF7DF;
$light-green-10: #F2F9F4;
$bright-lime-green: #B0F127;
$white-color: #ffffff;
$black-color: #0e314c;
$navlink-color: #4a6f8a;
$paragraph-color: rgb(77 82 93 / var(--tw-text-opacity, 1));
$purple-color: #c679e3;
$purple-100: #7E05FF;
$blue-violet: #716AD9;
$bg-color: #f7fafd;
$bg-color2: #f9f6f6;
$hosting-main-color: #032b8d;
$gradient-color: linear-gradient(135deg, #1fa954 0%, #43e794 100%);
$gradient-color2: linear-gradient(to right bottom, #1fa2ff, #00c0ff, #00d9f2, #53eedd, #a6ffcb);
$transition: .5s;
$font-size: 14px;

//custom variable
$red-color: #ff0000;
$dark-color: #000000;


$tw-blur: blur(0);
$tw-brightness: brightness(1);
$tw-contrast: contrast(1);
$tw-grayscale: grayscale(0);
$tw-hue-rotate: hue-rotate(0deg);
$tw-invert: invert(0);
$tw-saturate: saturate(1);
$tw-sepia: sepia(0);
$tw-drop-shadow: drop-shadow(0px 5px 15px rgba(6, 8, 15, 0.05));
