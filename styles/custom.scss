@import "variables";
@import "responsive";
@import "mixins";
@import "class_customs";

// #######################################
// =============== LANGUAGE ==============
// #######################################
// Language Switcher Styles

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: var(--inter-font-normal);
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: var(--inter-font-italic);
}

body {
  font-weight: 500;
  font-feature-settings: "frac", "tnum", "calt", "ccmp", "locl", "kern";
}

.language-switcher {
  position: relative;
  display: inline-block;

  .language-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: transparent;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    transition: all 0.3s ease;

    &:hover {
      background: #f8f9fa;
      border-color: $light-green-color;
    }

    .flag {
      font-size: 16px;
    }

    .lang-code {
      font-weight: 500;
      min-width: 20px;
    }
  }

  .language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 140px;
    margin-top: 4px;

    .language-option {
      display: flex;
      align-items: center;
      gap: 8px;
      width: 100%;
      padding: 10px 12px;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 14px;
      color: #333;
      transition: background-color 0.2s ease;

      &:hover {
        background: #f8f9fa;
      }

      &.active {
        background: $light-green-color;
        color: white;
      }

      .name {
        flex: 1;
        text-align: left;
      }
    }
  }
}

// Language Switcher in Others Option (Navbar)
.others-option {
  .language-switcher {
    margin-right: 15px;
  }
}

// #######################################

.swiper-pagination {
  &-bullets {
    position: unset !important;
    margin-top: 10px;
  }

  &-bullet {
    width: 10px !important;
    height: 10px !important;
    border-radius: 10px !important;

    @include duration(500);
  }
}

.page-title-area {
  h1 {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
  }
}

.main-text-area {
  &.landing-page-custom {
    h1 {
      font-size: 2rem;
    }

    h2 {
      font-size: 1.5rem;
    }

    h3 {
      font-size: 1.25rem;
    }

    li {
      font-size: 0.95rem;
    }
  }
}

.single-team {
  .team-image {
    &.team-image-custom {
      img {
        max-width: 125px;
      }
    }
  }
}

.dark .swiper-pagination-bullet {
  background: #fdfdfd !important;
}

.dark .swiper-pagination-bullet-active,
.swiper-pagination-bullet-active {
  width: 25px !important;
  background: #0e7444 !important;
}

.swiper-remove-time-transition > .swiper-wrapper {
  -webkit-transition-timing-function: linear;
  -o-transition-timing-function: linear;
  transition-timing-function: linear !important;
}

.partners__content-container {
  width: 170px;
  margin-top: 2.75rem; /* 11 * 0.25rem */
  margin-bottom: 2.75rem; /* 11 * 0.25rem */
  aspect-ratio: 1 / 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 32px;
  background-color: rgba(255, 255, 255, 0.1); /* bg-gray/10 */
  padding: 50px;
  transition: all 0.5s;
}

.partners__content-container:hover {
  transform: scale(1.05);
  background-color: #DF2E31;
  box-shadow: 0 5px 13px rgba(60, 72, 88, 0.90);
}


.startp-nav {
  .navbar-nav {
    .nav-item {
      .dropdown-menu {
        li {
          @include duration(300);

          &:hover {
            @include md {
              transform: scale(1.03);
            }
          }
        }
      }
    }
  }
}

.section-title {
  .title-detail {
    p {
      max-width: 100%;
      white-space: pre-line;
    }
  }
}

.services-content {
  .section-title {
    @include max_md {
      text-align: center;

      .bar {
        margin: 20px auto;
      }
    }
  }
}

.funfacts-area {
  &.funfacts-cslant-area {
    position: relative;

    .area-absolute {
      position: absolute;
      bottom: 0;
      left: 0;
      opacity: 60%;
      z-index: -1;
    }
  }
}

a {
  &.text-primary {
    color: $light-green-color !important;
  }
}

li {
  color: $paragraph-color;
}

.text-left {
  text-align: left;
}

.fill-current {
  fill: $light-green-color;
}

.rounded-md {
  border-radius: 0.375rem;
}

.drop-shadow-three {
  --tw-drop-shadow: drop-shadow(0px 5px 15px rgba(6, 8, 15, .05));
  filter: #{$tw-blur} #{$tw-brightness} #{$tw-contrast} #{$tw-grayscale} #{$tw-hue-rotate} #{$tw-invert} #{$tw-saturate} #{$tw-sepia} #{$tw-drop-shadow};
}

// #######################################
// =============== FOOTER ================
// #######################################
.single-footer-widget {
  ul {
    &.social-links {
      @include duration(300);

      li {
        a {
          &.youtube {
            border-color: $red-color;
            color: $red-color;

            &:hover {
              background: $red-color;
              color: $white-color;
            }
          }

          &.github {
            border-color: $dark-color;
            color: $dark-color;

            &:hover {
              background: $dark-color;
              color: $white-color;
            }
          }
        }
      }
    }

    &.footer-contact-info {
      li {
        @include max-screen(767) {
          .footer-contact-info-item {
            &.address {
              svg {
                display: none;
              }
            }

            svg {
              position: unset;
            }
          }
        }

        svg {
          top: 4px;
        }
      }
    }
  }

  @include max-screen(767) {
    .single-footer-item-title {
      font-weight: 700;
    }
  }
}

.footer-area {
  @include max-screen(767) {
    text-align: center;
  }

  &.footer-cslant-area {
    position: relative;

    .area-absolute {
      position: absolute;
      bottom: 6rem;
      left: 0;
      z-index: -1;

      &.right-shape {
        top: 3.5rem;
        right: 0;
        left: unset;

        @include max_sm {
          display: none;
        }
      }
    }
  }

  .footer-contact-info {
    @include max-screen(767) {
      text-align: center;
    }
  }
}

// ======== END FOOTER ======== //

// #######################################
// =============== CONTACT ===============
// #######################################
.contact-area {
  &.contact-cslant-area {
    position: relative;

    .area-absolute {
      position: absolute;
      top: -5px;
      right: 0;
      opacity: 60%;
      z-index: -1;
    }
  }
}

// #######################################
// =============== PARTNERS ==============
// #######################################
.tech-slide {
  width: 170px;
  height: 170px;
  padding: 50px;
  border-radius: 2rem; // ~32px
  margin: 2rem auto;
  background-color: rgba(128, 128, 128, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  transition: all 0.5s ease;

  .icon-default {
    transition: all 0.5s ease;
    position: absolute;
  }

  .icon-hover {
    display: none;
    transition: all 0.5s ease;
    position: absolute;
  }

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 13px rgba(60, 72, 88, 0.9);

    .icon-default {
      display: none;
    }

    .icon-hover {
      display: block;
    }
  }

  &.angular:hover {
    background-color: #DF2E31;
  }

  &.next:hover {
    background-color: #000;
  }

  &.vue:hover {
    background-color: #3FB984;
  }

  &.php:hover {
    background-color: #4F5B93;
  }

  &.laravel:hover {
    background-color: #FF291A;
  }

  &.yii:hover {
    background-color: #a6d6ff;
  }

  &.rails:hover {
    background-color: #ce140e;
  }

  &.wordpress:hover {
    background-color: #00749A;
  }

  &.html:hover {
    background-color: #E54C21;
  }

  &.react:hover {
    background-color: #01D8FF;
  }

  &.remix:hover {
    background-color: #121212;
  }
}

// ======= END PARTNERS ======= //

// #######################################
// ================ ABOUT ================
// #######################################
.about-content {
  .section-title {
    @include max_md {
      text-align: center;

      .bar {
        margin: 20px auto;
      }
    }
  }
}

.about-v1 {
  padding-top: 4rem;

  @include md {
    padding-top: 5rem;
  }

  h3 {
    font-size: 1.25rem;
  }

  .container {
    .content-wrapper {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin: 0 -1rem;

      .left-column {
        width: 100%;
        padding: 0 1rem;
      }

      .right-column {
        width: 100%;
        padding: 0 1rem;
      }
    }
  }

  &.about-v1-first-section {
    background-color: $light-green-10;

    @include rwd(1024) {
      padding-top: 7rem;
    }

    .section-title {
      width: 100%;
      margin-bottom: 100px;

      &.center {
        margin-left: auto;
        margin-right: auto;
        text-align: center;
      }

      h2 {
        margin-bottom: 16px;
        font-weight: bold;
        line-height: 1.2;
      }

      p {
        line-height: 1.75;
      }

      .text-left {
        text-align: left !important;
      }
    }

    .container {
      border-bottom: 1px solid rgba(var(--body-color), 0.15);
      padding-bottom: 4rem;

      @include md {
        padding-bottom: 5rem;
      }

      @include rwd(1024) {
        padding-bottom: 7rem;
      }

      .content-wrapper {
        .left-column {
          @include rwd(1024) {
            width: 50%;
          }

          .list-wrapper {
            margin-bottom: 3rem;

            @include rwd(1024) {
              margin-bottom: 0;
            }

            .list {
              display: flex;
              flex-wrap: wrap;
              margin: 0 -0.75rem;

              .list-column {
                width: 100%;
                padding: 0 0.75rem;

                @include sm {
                  width: 50%;
                }

                @include rwd(1024) {
                  width: 100%;
                }

                @include rwd(1280) {
                  width: 50%;
                }

                .list-item {
                  display: flex;
                  align-items: center;
                  margin-bottom: 1.25rem;
                  font-weight: 500;

                  .icon-wrapper {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 30px;
                    height: 30px;
                    margin-right: 1rem;
                    border-radius: 0.375rem;

                    @include bg_opacity($light-green-color, 0.1);
                  }
                }
              }
            }
          }
        }

        .right-column {
          @include rwd(1024) {
            width: 50%;
          }

          .image-wrapper {
            position: relative;
            margin: 0 auto;
            max-width: 500px;
            aspect-ratio: 25 / 24;

            .image {
              max-width: 100%;
              margin: 0 auto;
              box-shadow: var(--drop-shadow-three);

              &.dark {
                display: none;

                &.active {
                  display: block;
                  box-shadow: none;
                }
              }
            }
          }
        }
      }
    }
  }

  &.about-v1-second-section {
    padding-bottom: 4rem;

    @include md {
      padding-bottom: 5rem;
    }

    @include lg {
      padding-top: 7rem;
      padding-bottom: 7rem;
    }

    .container {
      .content-wrapper {
        .left-column {
          @include lg {
            width: 50%;
          }

          .image-wrapper {
            position: relative;
            margin: 0 auto;
            margin-bottom: 3rem;
            max-width: 500px;
            aspect-ratio: 25 / 24;
            text-align: center;

            @include lg {
              margin-bottom: 0;
            }

            .image {
              max-width: 100%;
              margin: 0 auto;
              box-shadow: var(--drop-shadow-three);

              &.dark {
                display: none;

                &.active {
                  display: block;
                  box-shadow: none;
                }
              }
            }
          }
        }

        .right-column {
          @include lg {
            width: 50%;
          }

          .text-wrapper {
            max-width: 600px;

            .text-block {
              margin-bottom: 2.25rem;

              &:last-child {
                margin-bottom: 0.25rem;
              }

              .title {
                margin-bottom: 1rem;
                font-weight: bold;
              }

              .paragraph {
                line-height: 1.75;

                @include sm {
                  line-height: 1.75;
                }
              }
            }
          }
        }
      }
    }
  }
}

// ======= END ABOUT ======= //

// #######################################
// ================= BLOG ================
// #######################################
.single-blog-post {
  .date {
    z-index: 1;
  }

  .blog-post-content {
    position: relative;
    height: 246px !important;
  }

  .read-more-btn {
    position: absolute;
    bottom: 20px;
    left: 20px;
  }

  .blog-image {
    img {
      &.blog-post-image {
        height: 290px;
        object-fit: cover;
      }
    }
  }
}

// ========== END BLOG ========== //

// #######################################
// =============== PRICING ===============
// #######################################
// Swiper Customization for Pricing
.pricing-slides {
  padding-bottom: 50px;

  .swiper-pagination-bullet {
    background-color: #2ecc71;
    opacity: 0.5;

    &-active {
      opacity: 1;
    }
  }

  .swiper-button-next,
  .swiper-button-prev {
    color: $green-color;

    &:hover {
      color: $green-color;
    }
  }
}

// Custom Navigation Buttons for Pricing Slider
.pricing-area {
  .position-relative {
    position: relative;
  }

  .shape2 {
    top: 50%;
  }

  .package-slider:hover {
    .custom-swiper-button-prev,
    .custom-swiper-button-next {
      background-color: $green-color;
      color: $white-color;
      display: flex;
    }
  }

  .pricing-table {
    &:hover {
      &.modern-pricing-table {
        margin: 0;

        .pricing-header {
          .pricing-title {
            transform: translateY(5px);
          }
        }
      }
    }

    &.modern-pricing-table {
      margin: 0;
    }
  }
}

// ======= END PRICING ======= //


.custom-swiper-button-prev,
.custom-swiper-button-next {
  position: absolute;
  top: 46%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background-color: $white-color;
  border-radius: 50%;
  display: none;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 10;
  color: $green-color;
  transition: all 0.3s ease;

  &:hover {
    background-color: $green-color;
    color: $white-color;
  }

  &.swiper-button-disabled {
    opacity: 0.5;
    cursor: default;
  }
}

.custom-swiper-button-prev {
  left: -20px;
}

.custom-swiper-button-next {
  right: -20px;
}

.pre-line {
  white-space: pre-line;
}

// Responsive adjustments
@media (max-width: 991px) {
  .custom-swiper-button-prev {
    left: 10px;
  }

  .custom-swiper-button-next {
    right: 10px;
  }
}

// #######################################
// =============== LINE CLAMP ============
// #######################################
@for $i from 1 through 12 {
  .line-clamp-#{$i} {
    display: -webkit-box;
    -webkit-line-clamp: #{$i};
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// #######################################
// ========== Categories, Tags ===========
// #######################################
.title-tags, .title-categories {
  font-size: 14px;
  font-weight: 500;
  color: $paragraph-color;
  margin-bottom: 0.5rem;
}

// #######################################
// ================= Category ================
// #######################################
.single-category-post {
  .category-post-content {
    height: 320px !important;
  }
}

// #######################################
// ================= Tag ================
// #######################################
.single-tag-post {
  .category-tag-content {
    height: 320px !important;
  }
}
