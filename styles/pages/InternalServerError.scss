@import "../variables";
@import "../mixins";

.internal-server-error {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5rem 1rem 4rem;

  @media (min-width: 768px) {
    padding-bottom: 5rem;
  }
  @media (min-width: 992px) {
    padding-bottom: 7rem;
    padding-top: 1rem;
  }

  &__container {
    width: 100%;
    max-width: 64rem;
    text-align: center;
    margin: 0 auto;
  }

  &__icon-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__icon {
    width: 100%;
    @media (min-width: 768px) {
      width: 80%;
    }
    @media (min-width: 1024px) {
      width: 70%;
    }
    margin: 0 auto;
  }

  &__title {
    color: $light-green-color;
    margin-top: 3rem;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 800;
    line-height: 1.25;
    letter-spacing: -0.025em;

    @media (min-width: 640px) {
      margin-top: 6rem;
      font-size: 2.25rem;
    }
    @media (min-width: 768px) {
      font-size: 3.5rem;
      line-height: 1;
    }
  }

  &__subtitle {
    margin-top: 0.5rem;
    text-align: center;
    font-size: 1.125rem;
    font-weight: 500;
    letter-spacing: -0.025em;
    color: var(--color-body, #64748b);

    @media (min-width: 640px) {
      font-size: 1.125rem;
    }
    @media (min-width: 768px) {
      font-size: 1.25rem;
    }
    // dark mode
    .dark & {
      color: var(--color-body-dark, #94a3b8);
    }
  }

  &__button-wrapper {
    margin-top: 3rem;
  }

  &__button {
    display: inline-block;
    padding: 0.75rem 2rem;
    font-size: 1rem;
    font-weight: 700;
    color: #fff;
    background-color: $light-green-color;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(37,99,235,0.1), 0 4px 6px -4px rgba(37,99,235,0.1);
    transition: background 0.3s, color 0.3s;

    @media (min-width: 768px) {
      padding-left: 2.25rem;
      padding-right: 2.25rem;
    }
    @media (min-width: 1024px) {
      padding-left: 2rem;
      padding-right: 2rem;
    }
    @media (min-width: 1280px) {
      padding-left: 2.25rem;
      padding-right: 2.25rem;
    }

    &:hover {
      background: #fff;
      color: $light-green-color;
      text-decoration: none;
    }
  }
}
