@import "../variables";
@import "../mixins";

.not-found-section {
  position: relative;
  z-index: 10;
  padding: 36px 0 16px;

  @media (min-width: 768px) {
    padding-bottom: 20px;
  }

  @include rwd(1024) {
    padding-bottom: 28px;
    padding-top: 180px;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
  }

  .flex-wrap {
    display: flex;
    flex-wrap: wrap;
    margin: -16px;
  }

  .full-width {
    width: 100%;
    padding: 16px;
  }

  .centered-content {
    max-width: 530px;
    text-align: center;
    margin: 0 auto;
  }

  .svg-container {
    margin-bottom: 36px;
    text-align: center;

    svg {
      width: 100%;
      height: auto;
    }
  }

  .title {
    margin-bottom: 16px;
    font-size: 1.875rem;
    font-weight: bold;
    color: #000;

    @include rwd(640) {
      font-size: 2.25rem;
    }
  }

  .description {
    margin-bottom: 40px;
    font-size: 1rem;
    color: #666;
    line-height: 1.625;

    @include rwd(640) {
      font-size: 1.25rem;
    }
  }

  .go-home-button {
    display: inline-block;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: bold;
    color: #fff;
    background-color: $light-green-color;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.3s;

    &:hover {
      background-color: #fff;
      color: $light-green-color;
      border: 1px solid $light-green-color;
    }
  }

  .background-errors {
    position: absolute;
    display: none;
    z-index: -1;

    @include rwd(1024) {
      display: block;
    }

    &.bg-one {
      bottom: 0;
      left: 0;
    }

    &.bg-two {
      top: 0;
      right: 0;
    }
  }
}
