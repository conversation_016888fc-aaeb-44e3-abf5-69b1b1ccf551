// ########################################
// ============ TRANSFORMATION ============
// ########################################
.bounce-infinite {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-3px);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

// ======= END TRANSFORMATION ======= //

// #######################################
// ============== DURATION ===============
// #######################################
// This mixin sets the transition duration for an element
@mixin duration($time) {
  transition-duration: #{$time}ms;
}

// Generate total 7 classes with different durations
// duration-100, duration-200, duration-300, duration-400, duration-500, duration-700, duration-1000
$durations: 100, 200, 300, 400, 500, 700, 1000;

@each $duration in $durations {
  .duration-#{$duration} {
    @include duration($duration);
  }
}

// ======= END DURATION ======= //


// #######################################
// =========== BACKGROUND CLASS ==========
// #######################################
@mixin bg_opacity($color, $opacity) {
  background-color: rgba($color, $opacity);
}

// ======= END BACKGROUND CLASS ======= //

// #######################################
// ========= CUSTOM BACKGROUND ===========
// #######################################
.partners__custom-background {
  background: linear-gradient(to right, #E3FDFD, #FFE6FA, #E3FDFD) !important;
  box-shadow: rgba(2, 2, 173, 0.25) 0 50px 100px -20px, rgba(17, 19, 54, 0.3) 0 30px 60px -30px, rgba(23, 69, 115, 0.35) 0 -2px 6px 0px inset !important;
  border-radius: 16px !important;
  padding: 5px 30px !important;
}

// ======= END CUSTOM BACKGROUND ======= //

// #######################################
// =========== CUSTOM TRUNCATE ===========
// #######################################
@mixin truncate($lines) {
  -webkit-line-clamp: $lines;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate {
  &-title a {
    @include truncate(2)
  }

  &-content p {
    @include truncate(2)
  }
}

// ======= END CUSTOM TRUNCATE ======= //
