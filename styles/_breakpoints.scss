// Small tablets and large smartphones (landscape view)
$screen-sm: 576px;

// Small tablets (portrait view)
$screen-md: 768px;

// Tablets and small desktops
$screen-lg: 992px;

// Large tablets and desktops
$screen-xl: 1200px;

// Desktops and large desktops
$screen-xxl: 1400px;

// add more breakpoints as needed
//==============================================================//
//============ CUSTOM BREAK POINT MEDIA QUERIES ================//
//==============================================================//

@function screen($size) {
  @return $size + px;
}

// Custom Breakpoints
$screen-lg-1024: screen(1024);
$screen-xl-1280: screen(1280);
$screen-xxl-1400: screen(1400);
$screen-xxl-1600: screen(1600);
$screen-xxl-1920: screen(1920);
