@import "breakpoints";

//===========================================//
//============ MIN MEDIA QUERIES ============//
//===========================================//

// Small devices
@mixin sm {
  @media (min-width: #{$screen-sm}) {
    @content;
  }
}

// Medium devices
@mixin md {
  @media (min-width: #{$screen-md}) {
    @content;
  }
}

// Large devices
@mixin lg {
  @media (min-width: #{$screen-lg}) {
    @content;
  }
}

// Extra large devices
@mixin xl {
  @media (min-width: #{$screen-xl}) {
    @content;
  }
}

// Custom devices
@mixin rwd($screen) {
  @media (min-width: ($screen + "px")) {
    @content;
  }
}


//===========================================//
//============ MAX MEDIA QUERIES ============//
//===========================================//

// Max small devices
@mixin max_sm {
  @media (max-width: #{$screen-sm}) {
    @content;
  }
}

// Max medium devices
@mixin max_md {
  @media (max-width: #{$screen-md}) {
    @content;
  }
}

// Max large devices
@mixin max_lg {
  @media (max-width: #{$screen-lg}) {
    @content;
  }
}

// Max extra large devices
@mixin max_xl {
  @media (max-width: #{$screen-xl}) {
    @content;
  }
}

// Max custom devices
@mixin max_rwd($screen) {
  @media (max-width: ($screen + "px")) {
    @content;
  }
}

//===========================================//
//============= CUSTOM MIXIN ================//
//===========================================//

// Mixin for min-width media query
@mixin min-screen($size) {
  @media (min-width: #{screen($size)}) {
    @content;
  }
}

// Mixin for max-width media query
@mixin max-screen($size) {
  @media (max-width: #{screen($size)}) {
    @content;
  }
}

//===========================================//
//============ EXAMPLE USAGE ================//
//===========================================//

// sample min
//.test {
//  font-size: 16px;
//  @include sm {
//    font-size: 12px;
//  }
//}

// sample max
//.test {
//  font-size: 16px;
//  @include max_sm {
//    font-size: 12px;
//  }
//}

// sample min custom with rwd (responsive web design)
//.test {
//  font-size: 16px;
//  @include rwd(1024px) {
//    font-size: 12px;
//  }
//}

// sample max custom
//.test {
//  font-size: 16px;
//  @include max_rwd(1024px) {
//    font-size: 12px;
//  }
//}

//.example {
//  font-size: 16px;
//
//  @include min-screen(1024) {
//    font-size: 18px;
//  }
//
//  @include max-screen(768) {
//    font-size: 14px;
//  }
//}

//===============================================//
//============ END EXAMPLE USAGE ================//
//===============================================//
