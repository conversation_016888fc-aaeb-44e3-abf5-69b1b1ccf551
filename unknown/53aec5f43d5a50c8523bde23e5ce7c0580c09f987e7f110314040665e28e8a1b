import { IBlogPost } from '@/types/blog';
import { IMetaSEO } from '@/types/app';

export type TCategoryResponse = {
  data: TCategoryData[],
  links?: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
  meta?: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
    links: Array<{
      url: string | null;
      label: string;
      active: boolean;
    }>;
    path: string;
  };
  error: boolean,
  message: string | null;
}

export type TCategoryData = {
  id: number,
  name: string;
  slug: string;
  icon: string;
  description: string;
  children: TCategoryData[];
  parent?: TParentCate;
  image: string;
  color: string;
  posts_count: number;
  total_views: string;
  total_likes: string;
  total_comments: string;
  total_score: string;
}

type TParentCate = {
  id: number;
  name: string;
  slug: string;
  icon: string;
  description: string;
}

export type TCategoryLoader = {
  category: TCategoryResponse,
  posts: IBlogPost,
  meta?: IMetaSEO,
}

export type CategoryParams = {
  page?: string | number;
  per_page?: string | number;
}
