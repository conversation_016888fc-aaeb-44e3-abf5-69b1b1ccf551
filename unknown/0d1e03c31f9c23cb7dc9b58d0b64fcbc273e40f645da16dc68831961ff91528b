"use client";

import React from "react";
import Link from "next/link";
import * as Icon from "react-feather";

// Services Data
const servicesData = [
  {
    id: 1,
    backgroundImage: "/images/repair-services-img/repair-services-img1.jpg",
    icon: <i className="flaticon-software"></i>,
    title: "Software Development IoT Solutions",
    description:
      "Lorem ipsum eiusmod dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 100,
  },
  {
    id: 2,
    backgroundImage: "/images/repair-services-img/repair-services-img2.jpg",
    icon: <i className="flaticon-gear"></i>,
    title: "Smart Home, Office IoT Solutions",
    description:
      "Lorem ipsum eiusmod dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 200,
  },
  {
    id: 3,
    backgroundImage: "/images/repair-services-img/repair-services-img3.jpg",
    icon: <i className="flaticon-skyline"></i>,
    title: "Smart City IoT Solutions",
    description:
      "Lorem ipsum eiusmod dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 300,
  },
  {
    id: 4,
    backgroundImage: "/images/repair-services-img/repair-services-img4.jpg",
    icon: <i className="flaticon-car-insurance"></i>,
    title: "Automative IoT Solutions",
    description:
      "Lorem ipsum eiusmod dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 400,
  },
  {
    id: 5,
    backgroundImage: "/images/repair-services-img/repair-services-img5.jpg",
    icon: <i className="flaticon-factory"></i>,
    title: "The Industrial IoT Solutions",
    description:
      "Lorem ipsum eiusmod dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 500,
  },
  {
    id: 6,
    backgroundImage: "/images/repair-services-img/repair-services-img6.jpg",
    icon: <i className="flaticon-chip"></i>,
    title: "System on Chip IoT Solutions",
    description:
      "Lorem ipsum eiusmod dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 600,
  },
];

const OurServices: React.FC = () => {
  return (
    <>
      <div className="iot-services-area pt-80 pb-50">
        <div className="container justify-content-center">
          <div className="section-title">
            <h2>Our Featured Services that We Provide</h2>
            <div className="bar"></div>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
          </div>

          <div className="row justify-content-center">
            {servicesData.map((service) => (
              <div
                key={service.id}
                className="col-lg-4 col-md-6"
                data-aos="fade-up"
                data-aos-delay={service.delay}
                data-aos-duration="700"
                data-aos-once="true"
              >
                <div
                  className="single-repair-services"
                  style={{
                    backgroundImage: `url(${service.backgroundImage})`,
                  }}
                >
                  <div className="icon">{service.icon}</div>

                  <h3>{service.title}</h3>
                  <p>{service.description}</p>

                  <Link href={service.link}>
                    <Icon.ArrowRight />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default OurServices;
