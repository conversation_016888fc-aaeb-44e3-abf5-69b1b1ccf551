{"name": "cslant-home-v2", "version": "4.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "watch-css": "sass --watch styles:styles", "build-css": "sass styles:styles"}, "dependencies": {"@types/aos": "^3.0.7", "@types/react-responsive-masonry": "^2.1.3", "animate.css": "^4.1.1", "aos": "^2.3.4", "axios": "^1.7.9", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "framer-motion": "^12.6.3", "fslightbox-react": "^1.7.6", "html-react-parser": "^5.2.5", "next": "^15.1.2", "next-intl": "^4.1.0", "nodemailer": "^6.9.16", "nodemailer-sendgrid-transport": "^0.2.0", "react": "^18.3.1", "react-accessible-accordion": "^5.0.0", "react-code-blocks": "^0.1.6", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-photo-view": "^1.2.7", "react-responsive-masonry": "^2.2.0", "react-tabs": "^6.0.2", "sharp": "^0.33.3", "sweetalert2": "^11.15.3", "sweetalert2-react-content": "^5.1.0", "swiper": "^11.0.7", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/fslightbox-react": "^1.7.8", "@types/node": "^22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "eslint": "^9.17.0", "eslint-config-next": "^15.1.2", "typescript": "^5.4.3"}, "packageManager": "yarn@4.8.1"}