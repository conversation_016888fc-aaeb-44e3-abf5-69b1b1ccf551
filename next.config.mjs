
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n.ts');

const isProd = process.env.NEXT_PUBLIC_APP_ENV === "production";
const isDev = process.env.NEXT_PUBLIC_APP_ENV === "local";
const protocol = process.env.NEXT_PUBLIC_IS_USE_HTTPS === 'true' ? 'https' : 'http';

/** @type {import('next').NextConfig} */
const nextConfig = {
  // For Static Export
  // output: 'export',
  trailingSlash: false,
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: protocol,
        hostname: process.env.NEXT_PUBLIC_ASSETS_DOMAIN,
        port: '*',
        pathname: '/**'
      }, {
        protocol: 'https',
        hostname: 'imgur.com',
        port: '',
        pathname: '/**'
      }
    ]
  },
  distDir: isProd ? process.env.NEXT_PUBLIC_DIST_DIR || 'dist' : undefined,

  /** Remove x-powered-by header */
  poweredByHeader: false,

  /** For asset using cdn https://nextjs.org/docs/app/api-reference/next-config-js/assetPrefix **/
  assetPrefix: isDev ? undefined : process.env.BASE_ASSET_PREFIX,
};

export default withNextIntl(nextConfig);
