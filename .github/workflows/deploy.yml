name: Deploy CSlant Server

on:
  push:
    branches: [ "main" ]

jobs:
  deploy:
    runs-on: self-hosted
    steps:
      - name: Deploy to Remote Host
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.REMOTE_HOST }}
          username: ${{ secrets.REMOTE_USERNAME }}
          password: ${{ secrets.REMOTE_PASS }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.REMOTE_PORT }}
          script: |
            ${{ vars.FE2_RUNNER_COMMAND }}
