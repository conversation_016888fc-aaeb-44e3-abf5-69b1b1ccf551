import { getRequestConfig } from 'next-intl/server';
import { headers } from 'next/headers';

// Can be imported from a shared config
export const locales = ['en', 'vi'] as const;
export const defaultLocale = 'en' as const;
export type Locale = (typeof locales)[number];

export default getRequestConfig(async ({ locale }) => {
  // If no locale is provided, try to get it from headers or use default
  if (!locale) {
    const headersList = await headers();
    const pathname = headersList.get('x-pathname') || '/';

    // Check if pathname starts with a locale
    const pathnameLocale = locales.find(loc =>
      pathname.startsWith(`/${loc}/`) || pathname === `/${loc}`
    );

    if (pathnameLocale) {
      locale = pathnameLocale;
    } else {
      // Use cookie or default
      const cookieLocale = headersList.get('x-locale') || defaultLocale;
      locale = locales.includes(cookieLocale as any) ? cookieLocale : defaultLocale;
    }
  }

  // Validate that the locale is valid
  if (!locales.includes(locale as any)) {
    locale = defaultLocale;
  }

  return {
    locale,
    messages: (await import(`../messages/${locale}.json`)).default
  };
});
