import { Metadata } from 'next';
import { getLocale } from 'next-intl/server';
import { assets } from "@/utils/helper";

interface GenerateMetadataOptions {
  title: string;
  description: string;
  path: string;
  locale?: string;
  imageUrl?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  tags?: string[];
  noindex?: boolean;
  metadataBase?: URL;
  titleTemplate?: string;
  icons?: {
    icon: Array<{ url: string; type?: string; sizes?: string }>;
    shortcut?: string;
    apple?: string;
  };
}

export async function generatePageMetadata(options: GenerateMetadataOptions): Promise<Metadata> {
  const {
    title,
    description,
    path,
    imageUrl = 'https://cslant.com/images/og-default.jpg',
    type = 'website',
    publishedTime,
    modifiedTime,
    authors = ['CSlant'],
    tags = [],
    noindex = false,
    metadataBase = new URL('https://cslant.com'),
    titleTemplate = '%s | CSlant',
    icons,
  } = options;

  const locale = options.locale || await getLocale();
  const baseUrl = metadataBase.toString().replace(/\/$/, ''); // Remove trailing slash if present

  // Ensure path starts with a slash
  const normalizedPath = path.startsWith('/') ? path : path ? `/${path}` : '';
  const canonicalPath = path ? normalizedPath : '';

  // Generate canonical URL without locale for default language (en)
  const canonicalUrl = new URL(canonicalPath, baseUrl).toString();

  // Generate localized URL for OpenGraph
  const url = new URL(
    locale === 'en' ? canonicalPath : `/${locale}${canonicalPath}`,
    baseUrl
  ).toString();

  const metadata: Metadata = {
    metadataBase: metadataBase,
    title: {
      default: title,
      template: titleTemplate,
    },
    description,
    creator: 'CSlant',
    authors: authors.map(author => ({ name: author, url: baseUrl })),
    publisher: 'CSlant',
    alternates: {
      canonical: canonicalUrl,
      languages: {
        en: `${baseUrl}/${path}`,
        vi: `${baseUrl}/vi` + normalizedPath,
        'x-default': `${baseUrl}/${path}`,
      },
    },
    other: {
      'lang': locale
    },
    ...(icons ? { icons } : {
      icons: {
        icon: [
          { url: assets('favicon.ico'), type: "image/x-icon" },
          { url: assets('favicon-16x16.png'), sizes: '16x16', type: 'image/png' },
          { url: assets('favicon-32x32.png'), sizes: '32x32', type: 'image/png' },
        ],
        shortcut: assets('favicon.ico'),
        apple: assets('apple-touch-icon.png'),
      },
    }),
    openGraph: {
      title,
      description,
      url,
      siteName: 'CSlant',
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type,
      ...(type === 'article' && {
        publishedTime,
        modifiedTime,
        authors,
        tags,
      }),
    } as const,  // Use const assertion to satisfy TypeScript's literal type checking
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [imageUrl],
    },
    robots: noindex ? {
      index: false,
      follow: true,
      googleBot: {
        index: false,
        follow: true,
      },
    } : {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };

  // Add article-specific metadata if type is 'article'
  if (type === 'article') {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'article',
      publishedTime,
      modifiedTime,
      authors: authors,
      tags: tags,
    } as const;  // Use const assertion to ensure type safety
  }

  return metadata;
}
