export const SITE_SUBTITLE = " | Technology Solutions Provider";

type TitleConfig = {
  title: string;
  template?: string;
};

type MetadataTitle = {
  title: string | {
    default: string;
    template: string;
  };
};

export function generateMetadataTitle(title: string | TitleConfig): MetadataTitle {
  // Nếu là string
  if (typeof title === 'string') {
    return {
      title: title.endsWith(SITE_SUBTITLE) ? title : title + SITE_SUBTITLE
    };
  }

  // Nếu là object có template
  if (title.template) {
    return {
      title: {
        default: title.title.endsWith(SITE_SUBTITLE) ? title.title : title.title + SITE_SUBTITLE,
        template: title.template.replace('%s', `%s${SITE_SUBTITLE}`)
      }
    };
  }

  // Nếu là object không có template
  return {
    title: {
      default: title.title.endsWith(SITE_SUBTITLE) ? title.title : title.title + SITE_SUBTITLE,
      template: `%s${SITE_SUBTITLE}`
    }
  }
}
