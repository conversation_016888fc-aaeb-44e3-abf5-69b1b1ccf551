import dayjs from 'dayjs';
import { Variant } from 'framer-motion';
import { ASSETS_URL, BLOG_ASSETS_URL } from '@/utils/env';

const DateFormat = 'MMMM DD, YYYY';

export const formatDate = (date: string) => {
  if (!date) {
    return 'Loading...';
  }
  return dayjs(date).format(DateFormat);
};

export const upperFirstChar = (title: string): string => {
  return String(title).charAt(0).toUpperCase() + String(title).slice(1);
};

export const generatePlaceholderImage = ({ x, y }: { x?: number; y?: number }) => {
  if (!x || !y) {
    return `${BLOG_ASSETS_URL}/app/core/core/base/images/placeholder.png`;
  }
  return `${BLOG_ASSETS_URL}/placeholder/${x}x${y}.png`;
};

const updateEffectPerItem = (index: number) => {
  switch (index) {
    case 0:
      return {
        opacity: 0,
        x: -50,
        y: -50
      };
    case 1:
      return {
        opacity: 0,
        y: -50
      };
    case 2:
      return {
        opacity: 0,
        x: 50,
        y: -50
      };
    case 3:
      return {
        opacity: 0,
        y: 50,
        x: -50
      };
    case 4:
      return {
        opacity: 0,
        y: 50
      };
    case 5:
      return {
        opacity: 0,
        y: 50,
        x: 50
      };
    case 6:
      return {
        opacity: 0,
        y: 0,
        x: 50
      };
    case 7:
      return {
        opacity: 0,
        y: 0,
        x: -50
      };
  }
};

export const effectVariants = (index: number) => ({
  hidden: updateEffectPerItem(index) as Variant,
  visible: {
    opacity: 1,
    y: 0,
    x: 0
  }
});

export const assets = (asset: string) => {
  if (!asset) {
    return `${BLOG_ASSETS_URL}/app/core/core/base/images/placeholder.png`;
  }

  if (asset.startsWith('http')) {
      return asset;
  }

  if (asset.startsWith('/')) {
      return `${ASSETS_URL}${asset}`;
  }

  return `${ASSETS_URL}/${asset}`;
}

export const formatTitleNormalized = (title: string): string => {
  return title.replace(/[^\w\s]/gi, '').replace(/([a-z])([A-Z])/g, '$1 $2').toLowerCase().trim().replace(/\s+/g, '-')
};

export const getAltImage = (urlImage: string) => {
  if (!urlImage) {
    return 'CSlant placeholder image';
  }

  const url = new URL(urlImage);
  const pathname = url.pathname;
  const filename = pathname.substring(pathname.lastIndexOf('/') + 1);

  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1) {
    return filename;
  }

  const namePart = filename.substring(0, lastDotIndex).replace(/[-_]/g, ' ').replace(/\b\w/g, char => char.toUpperCase());

  const extension = filename.substring(lastDotIndex).toLowerCase();

  return namePart + extension;
};
