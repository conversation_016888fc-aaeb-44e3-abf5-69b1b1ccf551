'use client';

import React from 'react';
import * as Icon from 'react-feather';
import Link from 'next/link';
import Image from 'next/image';
import { TCategoryData } from '@/types/blog/category';
import { generatePlaceholderImage, getAltImage } from '@/utils/helper';
import { useTranslations } from 'next-intl';

interface CategoryGridProps {
  categories: TCategoryData[];
}

const CategoryGrid: React.FC<CategoryGridProps> = ({ categories }) => {
  const t = useTranslations('pages.categories');

  return (
    <>
      {categories.map((category) => (
        <div key={category.id} className="col-lg-4 col-md-6">
          <div className="single-blog-post">
            <div className="blog-image category-image">
              <Link href={`/blog/category/${category.slug}`}>
                <Image
                  src={category.image ?? generatePlaceholderImage({ x: 860, y: 700 })}
                  alt={getAltImage(category.image)}
                  width={860}
                  height={700}
                />
              </Link>

              <div className="date">
                <Icon.Tag /> {category.posts_count || 0} {t(category.posts_count > 1 ? 'posts' : 'post')}
              </div>
            </div>

            <div className="blog-post-content">
              <h3>
                <Link href={`/blog/category/${category.slug}`}>
                  {category.name}
                </Link>
              </h3>

              {category.description && (
                <p className="line-clamp-5" dangerouslySetInnerHTML={{ __html: category.description }} />
              )}

              <Link
                href={`/blog/category/${category.slug}`}
                className="read-more-btn"
              >
                {t('view-post', { count: category.posts_count })} <Icon.ArrowRight />
              </Link>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default CategoryGrid;
