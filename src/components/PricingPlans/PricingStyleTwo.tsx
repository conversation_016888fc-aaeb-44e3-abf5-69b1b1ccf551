"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";

// Shape Images
import shape2 from "/public/images/shape2.svg";
import shape4 from "/public/images/shape4.svg";

const plans = [
  {
    id: 1,
    iconClass: "flaticon-data",
    title: "Web Hosting",
    price: 10.99,
    billingCycle: "/m",
    features: [
      "99.9% Uptime Guarantee",
      "Reliable & Secure",
      "Powered by cPanel / Plesk",
      "Web Asset Delivery",
      "24/7 Dedicated Support",
    ],
    isActive: false,
  },
  {
    id: 2,
    iconClass: "flaticon-cloud",
    title: "Cloud Hosting",
    price: 13.99,
    billingCycle: "/m",
    features: [
      "99.9% Uptime Guarantee",
      "Reliable & Secure",
      "Powered by cPanel / Plesk",
      "Web Asset Delivery",
      "24/7 Dedicated Support",
    ],
    isActive: true,
  },
  {
    id: 3,
    iconClass: "flaticon-vps",
    title: "Vps Hosting",
    price: 15.99,
    billingCycle: "/m",
    features: [
      "99.9% Uptime Guarantee",
      "Reliable & Secure",
      "Powered by cPanel / Plesk",
      "Web Asset Delivery",
      "24/7 Dedicated Support",
    ],
    isActive: false,
  },
];

const PricingStyleTwo = () => {
  return (
    <div className="pricing-area pt-80 pb-50 bg-f7fafd">
      <div className="container">
        <div className="section-title">
          <h2>Pricing Plans</h2>
          <div className="bar"></div>
          <p>
            Flexible pricing plans tailored to your needs and budget.<br />
            Choose the perfect package to grow your business with confidence.
          </p>
        </div>

        <div className="row justify-content-center">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`col-lg-4 col-md-6 ${
                plan.isActive ? "active-plan" : ""
              }`}
              data-aos="fade-up"
              data-aos-delay={`${plan.id * 100}`}
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className="single-pricing-table">
                <div className="pricing-header">
                  <i className={plan.iconClass}></i>
                  <h3>{plan.title}</h3>
                </div>

                <div className="price">
                  <span>
                    <sup>$</sup>
                    {plan.price.toFixed(2)}
                    <span>{plan.billingCycle}</span>
                  </span>
                </div>

                <div className="pricing-features">
                  <ul>
                    {plan.features.map((feature, index) => (
                      <li key={index}>
                        <i data-feather="check"></i> {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="pricing-footer">
                  <Link href="#" className="btn btn-primary">
                    Select Plan
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Shape Images */}
      <div className="shape8 rotateme">
        <Image src={shape2} alt="shape" width={22} height={22} />
      </div>
      <div className="shape2 rotateme">
        <Image src={shape2} alt="shape" width={22} height={22} />
      </div>
      <div className="shape7">
        <Image src={shape4} alt="shape" width={21} height={20} />
      </div>
      <div className="shape4">
        <Image src={shape4} alt="shape" width={21} height={20} />
      </div>
    </div>
  );
};

export default PricingStyleTwo;
