"use client";

import React from "react";
import Link from "next/link";
import * as Icon from "react-feather";
import Image from "next/image";

// Shape Images
import shape1 from "/public/images/shape1.png";
import shape2 from "/public/images/shape2.svg";
import shape3 from "/public/images/shape3.svg";
import shape4 from "/public/images/shape4.svg";

const pricingPlans = [
  {
    id: 1,
    title: "Free",
    description: "Get your business up and running",
    price: 0,
    billingCycle: "/m",
    buttonText: "Get Started Free",
    trial: null,
    features: [
      "Drag & Drop Builder",
      "Lead Generation & Sales",
      "Boot & Digital Assistants",
      "Customer Service",
      "Up to 1000 Subscribers",
      "Unlimited Broadcasts",
      "Landing Pages & Web Widgets",
    ],
  },
  {
    id: 2,
    title: "Pro",
    description: "Get your business up and running",
    price: 149,
    billingCycle: "/m",
    buttonText: "Start 3 Days Free Trial",
    trial: 3,
    features: [
      "Drag & Drop Builder",
      "Lead Generation & Sales",
      "Boot & Digital Assistants",
      "Customer Service",
      "Up to 3300 Subscribers",
      "Unlimited Broadcasts",
      "Landing Pages & Web Widgets",
    ],
  },
  {
    id: 3,
    title: "Premium",
    description: "Get your business up and running",
    price: 179,
    billingCycle: "/m",
    buttonText: "Start 6 Days Free Trial",
    trial: 6,
    features: [
      "Drag & Drop Builder",
      "Lead Generation & Sales",
      "Boot & Digital Assistants",
      "Customer Service",
      "Up to 10000 Subscribers",
      "Unlimited Broadcasts",
      "Landing Pages & Web Widgets",
    ],
  },
];

const MonthlyPlan = () => {
  return (
    <>
      <div className="row justify-content-center">
        {pricingPlans.map((plan, index) => (
          <div
            key={plan.id}
            className="col-lg-4 col-md-6 col-sm-6"
            data-aos="fade-up"
            data-aos-delay={`${(index + 1) * 100}`}
            data-aos-duration="700"
            data-aos-once="true"
          >
            <div className="pricing-box">
              <div className="pricing-header">
                <h3>{plan.title}</h3>
                <p>{plan.description}</p>
              </div>

              <div className="price">
                ${plan.price} <span>{plan.billingCycle}</span>
              </div>

              <div className="buy-btn">
                <Link href="#" className="btn btn-primary">
                  {plan.buttonText}
                </Link>
              </div>

              <ul className="pricing-features">
                {plan.features.map((feature, i) => (
                  <li key={i}>
                    <Icon.Check /> {feature}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>

      {/* Shape Images */}
      <div className="shape1">
        <Image src={shape1} alt="shape" width={202} height={202} />
      </div>
      <div className="shape2 rotateme">
        <Image src={shape2} alt="shape" width={22} height={22} />
      </div>
      <div className="shape3">
        <Image src={shape3} alt="shape" width={28} height={28} />
      </div>
      <div className="shape4">
        <Image src={shape4} alt="shape" width={21} height={20} />
      </div>
      <div className="shape7">
        <Image src={shape4} alt="shape" width={21} height={20} />
      </div>
      <div className="shape8 rotateme">
        <Image src={shape2} alt="shape" width={22} height={22} />
      </div>
    </>
  );
};

export default MonthlyPlan;
