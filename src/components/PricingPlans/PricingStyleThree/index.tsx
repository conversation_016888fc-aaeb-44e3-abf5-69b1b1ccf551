"use client";

import React from "react";
import Image from "next/image";
import { Tab, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bPanel } from "react-tabs";
import MonthlyPlan from "./MonthlyPlan";
import YearlyPlan from "./YearlyPlan";

// Shape Images
import shape1 from "/public/images/agency-image/agency-shape1.png"; 


const PricingStyleFour = () => {
  return (
    <>
      <div className="pricing-area pb-50">
        <div className="container">
          <div className="section-title">
            <h2>Our Pricing Plan</h2>
            <div className="bar"></div>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
          </div>

          <Tabs className="pricing-tab bg-color">
            <TabList>
              <Tab>Monthly Plan</Tab>
              <Tab>Yearly Plan</Tab>
            </TabList>

            <TabPanel>
              <MonthlyPlan />
            </TabPanel>

            <TabPanel>
              <YearlyPlan />
            </TabPanel>
          </Tabs>
        </div>

        {/* Shape Image */}
        <div className="shape9 mobile-dnone">
          <Image
            src={shape1}
            alt="image"
            width={299}
            height={278}
          />
        </div>
      </div>
    </>
  );
};

export default PricingStyleFour;
