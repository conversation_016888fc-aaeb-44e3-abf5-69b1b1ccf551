"use client";

import React, { useState, useRef, useEffect } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import * as Icon from "react-feather";

const LanguageSwitcher: React.FC = () => {
  const t = useTranslations('common');
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const languages = [
    { code: 'en', name: t('english'), flag: '🇺🇸' },
    { code: 'vi', name: t('vietnamese'), flag: '🇻🇳' }
  ];

  const currentLanguage = languages.find(lang => lang.code === locale) || languages[0];

  const handleLanguageChange = (newLocale: string) => {
    // Set cookie for locale preference
    document.cookie = `cslant_locale=${newLocale}; path=/; max-age=31536000`; // 1 year

    if (!pathname) {
      // If no pathname, just reload to apply new locale
      window.location.reload();
      return;
    }

    const hasLocalePrefix = pathname.startsWith('/en/') || pathname.startsWith('/vi/') ||
      pathname === '/en' || pathname === '/vi';

    if (hasLocalePrefix) {
      // Replace current locale with new one (e.g., /en/about -> /vi/about)
      const pathWithoutLocale = pathname.replace(/^\/(en|vi)/, '') || '/';
      router.push(`/${newLocale}${pathWithoutLocale}`);
    } else {
      // For paths without locale prefix (e.g., /about), just reload to apply new locale
      window.location.reload();
    }

    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="language-switcher" ref={dropdownRef}>
      <button
        className="language-btn"
        onClick={() => setIsOpen(!isOpen)}
        aria-label={t('language')}
      >
        <span className="flag">{currentLanguage.flag}</span>
        <span className="lang-code">{currentLanguage.code.toUpperCase()}</span>
        <Icon.ChevronDown size={16} />
      </button>

      {isOpen && (
        <div className="language-dropdown">
          {languages.map((language) => (
            <button
              key={language.code}
              className={`language-option ${locale === language.code ? 'active' : ''}`}
              onClick={() => handleLanguageChange(language.code)}
            >
              <span className="flag">{language.flag}</span>
              <span className="name">{language.name}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default LanguageSwitcher;
