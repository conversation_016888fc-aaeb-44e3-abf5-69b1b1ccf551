'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import * as Icon from 'react-feather';

// Shape Images
import { assets } from '@/utils/helper';
import { LOGO, MAP, SHAPE_IMAGE, SHAPE_SVG } from '@/constants/asset';
import {
  CSLANT_ADDRESS_SHORT,
  CSLANT_CONTACT_EMAIL,
  CSLANT_PHONE_DISPLAY,
  CSLANT_PHONE_NUMBER,
  SOCIAL_FACEBOOK_URL,
  SOCIAL_GITHUB_URL,
  SOCIAL_GOOGLE_MAP_URL,
  SOCIAL_LINKEDIN_URL,
  SOCIAL_TWITTER_URL,
  SOCIAL_YOUTUBE_URL
} from '@/constants/app';
import { useTranslations } from 'next-intl';

const Footer = () => {
  const t = useTranslations('footer');
  const currentYear = new Date().getFullYear();

  const companyLinks = [
    { href: '/about', label: t('about-us') },
    { href: '/services', label: t('services') },
    { href: 'https://docs.cslant.com', label: t('documentation'), target: '_blank' },
    { href: '/blog', label: t('lasted-news') }
  ];

  const supportLinks = [
    { href: '/faq', label: t('faqs') },
    { href: '/privacy-policy', label: t('privacy-policy') },
    { href: '/cookie-policy', label: t('cookie-policy') },
    { href: '/terms-conditions', label: t('terms-conditions') },
    { href: '/team', label: t('team') },
    { href: '/contact', label: t('contact-us') }
  ];

  const contactInfo = [
    {
      icon: <Icon.MapPin />,
      link: SOCIAL_GOOGLE_MAP_URL,
      linkText: CSLANT_ADDRESS_SHORT,
      className: 'address'
    },
    {
      icon: <Icon.Mail />,
      text: 'Email: ',
      link: `mailto:${CSLANT_CONTACT_EMAIL}`,
      linkText: CSLANT_CONTACT_EMAIL,
      className: 'email'
    },
    {
      icon: <Icon.PhoneCall />,
      text: t('phone'),
      link: `tel:${CSLANT_PHONE_NUMBER}`,
      linkText: CSLANT_PHONE_DISPLAY,
      className: 'phone'
    }
  ];

  const socialLinks = [
    {
      href: SOCIAL_GITHUB_URL,
      icon: <Icon.GitHub />,
      className: 'github'
    },
    {
      href: SOCIAL_FACEBOOK_URL,
      icon: <Icon.Facebook />,
      className: 'facebook'
    },
    {
      href: SOCIAL_TWITTER_URL,
      icon: <Icon.Twitter />,
      className: 'twitter'
    },
    {
      href: SOCIAL_YOUTUBE_URL,
      icon: <Icon.Youtube />,
      className: 'youtube'
    },
    {
      href: SOCIAL_LINKEDIN_URL,
      icon: <Icon.Linkedin />,
      className: 'linkedin'
    }
  ];

  return (
    <footer className="footer-area footer-cslant-area bg-f7fafd">
      <div className="container">
        <div className="row">
          {/* Logo and Description */}
          <div
            className="col-lg-3 col-md-6"
            data-aos="fade-in"
            data-aos-delay="100"
            data-aos-duration="700"
            data-aos-once="true"
          >
            <div className="single-footer-widget">
              <div className="logo">
                <Link href="/">
                  <Image src={assets(LOGO)} alt="logo" width={110} height={36} />
                </Link>
              </div>
              <p>{t('description')}</p>
            </div>
          </div>

          {/* Company Links */}
          <div
            className="col-lg-3 col-md-6"
            data-aos="fade-in"
            data-aos-delay="200"
            data-aos-duration="700"
            data-aos-once="true"
          >
            <div className="single-footer-widget ps-5">
              <h3 className="single-footer-item-title">{t('company')}</h3>
              <ul className="list">
                {companyLinks.map((link, index) => (
                  <li key={index} className="duration-300">
                    <Link href={link.href} {...link.target ? { target: link.target } : {}}>{link.label}</Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Support Links */}
          <div
            className="col-lg-3 col-md-6"
            data-aos="fade-in"
            data-aos-delay="300"
            data-aos-duration="700"
            data-aos-once="true"
          >
            <div className="single-footer-widget">
              <h3 className="single-footer-item-title">{t('support')}</h3>
              <ul className="list">
                {supportLinks.map((link, index) => (
                  <li key={index} className="duration-300">
                    <Link href={link.href}>{link.label}</Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Contact Info */}
          <div
            className="col-lg-3 col-md-6"
            data-aos="fade-in"
            data-aos-delay="400"
            data-aos-duration="700"
            data-aos-once="true"
          >
            <div className="single-footer-widget">
              <h3 className="single-footer-item-title">{t('address')}</h3>
              <ul className="footer-contact-info">
                {contactInfo.map((info, index) => (
                  <li key={index}>
                    <p className={`footer-contact-info-item ${info.className}`}>
                      {info.icon} {info.text}
                      {info.link && <a href={info.link} target={'_blank'}>{info.linkText}</a>}
                    </p>
                  </li>
                ))}
              </ul>

              <ul className="social-links">
                {socialLinks.map((social, index) => (
                  <li key={index}>
                    <a
                      href={social.href}
                      className={social.className}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {social.icon}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Copyright */}
          <div className="col-lg-12 col-md-12">
            <div className="copyright-area">
              <p>
                {t('copyright', { year: currentYear })}
              </p>
            </div>
          </div>
        </div>
      </div>

      <Image src={assets(MAP)} className="map" alt="map" width={910} height={443} />

      {/* Shape Images */}
      <div className="shape1">
        <Image src={assets(SHAPE_IMAGE(1))} alt="shape" width={202} height={202} />
      </div>
      <div className="shape8 rotateme">
        <Image src={assets(SHAPE_SVG(2))} alt="shape" width={22} height={22} />
      </div>
      <div className="area-absolute right-shape">
        <Image src={assets('/images/intro/area-4.svg')} alt="shape" width={55} height={99} />
      </div>
      <div className="area-absolute">
        <Image src={assets('/images/intro/area-2.svg')} alt="shape" width={79} height={94} />
      </div>
    </footer>
  );
};

export default Footer;
