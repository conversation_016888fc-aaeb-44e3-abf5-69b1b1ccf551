"use client";

import React from "react";
import * as Icons from "react-feather";
import Link from "next/link";

const Features: React.FC = () => {
  return (
    <>
      <div className="hosting-features-area pt-80 pb-50 bg-f9f6f6">
        <div className="container">
          <div className="section-title">
            <h2>Our Features</h2>
            <div className="bar"></div>
            <p>
              Discover powerful features that enhance efficiency and user experience.<br />
              Built for performance, scalability, and ease of use.
            </p>
          </div>

          <div className="row justify-content-center">
            <div
              className="col-lg-4 col-md-6"
              data-aos="fade-up"
              data-aos-delay="100"
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className="single-hosting-features">
                <div className="icon">
                  <Icons.Settings />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Incredible Infrastructure
                  </Link>
                </h3>
                <p>
                  Designed for speed, stability, and scalability to meet the demands of your growing business.
                </p>
              </div>
            </div>

            <div
              className="col-lg-4 col-md-6"
              data-aos="fade-up"
              data-aos-delay="200"
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className="single-hosting-features">
                <div className="icon bg-c679e3">
                  <Icons.Info />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Information Retrieval
                  </Link>
                </h3>
                <p>
                  Find, access, and organize critical information quickly — right when you need it most.
                </p>
              </div>
            </div>

            <div
              className="col-lg-4 col-md-6"
              data-aos="fade-up"
              data-aos-delay="300"
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className="single-hosting-features">
                <div className="icon">
                  <Icons.Bell />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Best Analytics Audits
                  </Link>
                </h3>
                <p>
                  Lorem ipsum dolor amet, adipiscing, sed do eiusmod tempor
                  incididunt ut labore dolore magna aliqua.
                </p>
              </div>
            </div>

            <div
              className="col-lg-4 col-md-6"
              data-aos="fade-up"
              data-aos-delay="400"
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className="single-hosting-features">
                <div className="icon bg-c679e3">
                  <Icons.Grid />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Simple Dashboard
                  </Link>
                </h3>
                <p>
                  Manage everything effortlessly with a clean, intuitive dashboard built for clarity and control.
                </p>
              </div>
            </div>

            <div
              className="col-lg-4 col-md-6"
              data-aos="fade-up"
              data-aos-delay="500"
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className="single-hosting-features">
                <div className="icon">
                  <Icons.Mail />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Email Notifications
                  </Link>
                </h3>
                <p>
                  Receive instant alerts and updates through automated email notifications, so you never miss a beat.                </p>
              </div>
            </div>

            <div
              className="col-lg-4 col-md-6"
              data-aos="fade-up"
              data-aos-delay="600"
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className="single-hosting-features">
                <div className="icon bg-c679e3">
                  <Icons.HardDrive />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Deep Technical SEO
                  </Link>
                </h3>
                <p>
                  Lorem ipsum dolor amet, adipiscing, sed do eiusmod tempor
                  incididunt ut labore dolore magna aliqua.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Features;
