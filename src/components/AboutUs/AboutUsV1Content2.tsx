"use client";

import EffectWrapper from '@/components/EffectWrapper';
import { assets, effectVariants } from '@/utils/helper';
import Image from "next/image";
import { useTranslations } from 'next-intl';

const AboutUsV1Content2 = () => {
  const t = useTranslations('pages.about');

  return (
    <section className="about-v1 about-v1-second-section">
      <div className="container">
        <div className="content-wrapper">
          <div className="left-column">
            <EffectWrapper variants={effectVariants(4)} delayTime={0.7}>
              <div className="image-wrapper">
                <Image
                  src={assets('/images/about/about-image-2.svg')}
                  alt="light cslant about"
                  className="image light drop-shadow-three"
                  width={500}
                  height={480}
                />
                <Image
                  src={assets('images/about/about-image-2-dark.svg')}
                  alt="dark cslant about"
                  className="image dark"
                  width={500}
                  height={480}
                />
              </div>
            </EffectWrapper>
          </div>
          <div className="right-column">
            <EffectWrapper variants={effectVariants(1)} delayTime={0.5}>
              <div className="text-wrapper">
                <div className="text-block">
                  <h3 className="title">
                    {t('comprehensive')}
                  </h3>
                  <p className="paragraph">
                    {t('comprehensiveDescription')}
                  </p>
                </div>
                <div className="text-block">
                  <h3 className="title">
                    {t('dedicated')}
                  </h3>
                  <p className="paragraph">
                    {t('dedicatedDescription')}
                  </p>
                </div>
                <div className="text-block">
                  <h3 className="title">
                    {t('seamless')}
                  </h3>
                  <p className="paragraph">
                    {t('seamlessDescription')}
                  </p>
                </div>
              </div>
            </EffectWrapper>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutUsV1Content2;
