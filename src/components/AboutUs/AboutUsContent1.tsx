'use client';

import React from 'react';
import Image from 'next/image';

import aboutImg from '/public/images/about-one.png';
import { useTranslations } from 'next-intl';

const AboutUsContent1 = () => {
  const t = useTranslations('pages.about');

  return (
    <>
      <div className="about-area ptb-80">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6 col-md-12">
              <div className="about-image">
                <Image
                  src={aboutImg}
                  alt="image"
                  width={685}
                  height={494}
                />
              </div>
            </div>

            <div className="col-lg-6 col-md-12">
              <div className="about-content">
                <div className="section-title">
                  <h2>{t('aboutUs')}</h2>
                  <div className="bar"></div>
                  <p>{t('aboutUsDescription1')}</p>
                </div>

                <p>{t('aboutUsDescription2')}</p>

                <p>{t('aboutUsDescription3')}</p>

                <p>{t('aboutUsDescription4')}</p>
              </div>
            </div>
          </div>

          <div className="about-inner-area">
            <div className="row justify-content-center">
              <div className="col-lg-4 col-md-6 col-sm-6">
                <div className="about-text">
                  <h3>{t('ourHistory')}</h3>
                  <p className="pre-line">
                    {t('ourHistoryDescription')}
                  </p>
                </div>
              </div>

              <div className="col-lg-4 col-md-6 col-sm-6">
                <div className="about-text">
                  <h3>{t('ourMission')}</h3>
                  <p className="pre-line">
                    {t('ourMissionDescription')}
                  </p>
                </div>
              </div>

              <div className="col-lg-4 col-md-6 col-sm-6">
                <div className="about-text">
                  <h3>{t('whoWeAre')}</h3>
                  <p className="pre-line">
                    {t('whoWeAreDescription')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AboutUsContent1;
