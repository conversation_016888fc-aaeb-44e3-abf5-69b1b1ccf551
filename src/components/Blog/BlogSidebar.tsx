"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import * as Icon from "react-feather";
import { useLocale } from 'next-intl';
import { categoryApi } from '@/api/blog/category';
import { tagApi } from '@/api/blog/tag';
import { TCategoryData } from '@/types/blog/category';
import { TTagData } from '@/types/blog/tag';

import blogImg1 from "/public/images/blog-image/blog7.jpg";
import blogImg2 from "/public/images/blog-image/blog8.jpg";
import blogImg3 from "/public/images/blog-image/blog9.jpg";


const BlogSidebar = () => {
  const [categories, setCategories] = useState<TCategoryData[]>([]);
  const [tags, setTags] = useState<TTagData[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [tagsLoading, setTagsLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setCategoriesLoading(true);
        const categoriesResponse = await categoryApi.get<TCategoryData[]>({
          params: {
            per_page: 5
          }
        });

        if (categoriesResponse.data) {
          setCategories(categoriesResponse.data);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setCategoriesLoading(false);
      }
    };

    const fetchTags = async () => {
      try {
        setTagsLoading(true);
        const tagsResponse = await tagApi.get<TTagData[]>({
          params: {
            per_page: 15
          }
        });

        if (tagsResponse.data) {
          setTags(tagsResponse.data);
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
      } finally {
        setTagsLoading(false);
      }
    };

    fetchCategories();
    fetchTags();
  }, []);

  return (
    <>
      <div className="widget-area sticky-sidebar" id="secondary">
        {/*<div className="widget widget_search">*/}
        {/*  <form className="search-form">*/}
        {/*    <label>*/}
        {/*      <input*/}
        {/*        type="search"*/}
        {/*        className="search-field"*/}
        {/*        placeholder="Search..."*/}
        {/*      />*/}
        {/*    </label>*/}
        {/*    <button type="submit">*/}
        {/*      <Icon.Search />*/}
        {/*    </button>*/}
        {/*  </form>*/}
        {/*</div>*/}

        <div className="widget widget_startp_posts_thumb">
          <h3 className="widget-title">Popular Posts</h3>

          <article className="item">
            <Link href="/blog/blog-details/" className="thumb" >
              <span
                className="fullimage cover"
                role="img"
                style={{ backgroundImage: `url(${blogImg1.src})` }}
              ></span>
            </Link>

            <div className="info">
              <time>June 10, 2024</time>
              <h4 className="title usmall">
                <Link href="/blog/blog-details/" >
                  Making Peace With The Feast Or Famine Of Freelancing
                </Link>
              </h4>
            </div>

            <div className="clear"></div>
          </article>

          <article className="item">
            <Link href="/blog/blog-details/" className="thumb" >
              <span
                className="fullimage cover"
                role="img"
                style={{ backgroundImage: `url(${blogImg2.src})` }}
              ></span>
            </Link>
            <div className="info">
              <time>June 21, 2024</time>
              <h4 className="title usmall">
                <Link href="/blog/blog-details/" >
                  I Used The Web For A Day On A 50 MB Budget
                </Link>
              </h4>
            </div>

            <div className="clear"></div>
          </article>

          <article className="item">
            <Link href="/blog/blog-details/" className="thumb" >
              <span
                className="fullimage cover"
                role="img"
                style={{ backgroundImage: `url(${blogImg3.src})` }}
              ></span>
            </Link>
            <div className="info">
              <time>June 30, 2024</time>
              <h4 className="title usmall">
                <Link href="/blog/blog-details/" >
                  How To Create A Responsive Popup Gallery?
                </Link>
              </h4>
            </div>

            <div className="clear"></div>
          </article>
        </div>

        <div className="widget widget_categories">
          <h3 className="widget-title">Categories</h3>

          {categoriesLoading ? (
            <div className="lds-roller">
              <div></div><div></div><div></div><div></div>
              <div></div><div></div><div></div><div></div>
            </div>
          ) : (
            <ul>
              {categories && categories.length > 0 ? (
                categories.map((category) => (
                  <li key={category.id}>
                    <Link href={`/blog/category/${category.slug}`}>
                      {category.name}
                    </Link>
                  </li>
                ))
              ) : (
                <li>
                  <span>No categories available</span>
                </li>
              )}
            </ul>
          )}
        </div>

        <div className="widget widget_tag_cloud">
          <h3 className="widget-title">Tags</h3>

          {tagsLoading ? (
            <div className="lds-roller">
              <div></div><div></div><div></div><div></div>
              <div></div><div></div><div></div><div></div>
            </div>
          ) : (
            <div className="tagcloud">
              {tags && tags.length > 0 ? (
                tags.map((tag) => (
                  <Link key={tag.id} href={`/blog/tag/${tag.slug}`}>
                    {tag.name}{' '}{'('}
                    {tag.posts_count && (
                      <span className="tag-link-count">{tag.posts_count}</span>
                    )}
                    {')'}
                  </Link>
                ))
              ) : (
                <span>No tags available</span>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default BlogSidebar;
