"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { commentApi } from '@/api/blog/comment';
import { TCommentItem } from '@/types/blog/comment';
import { generatePlaceholderImage } from '@/utils/helper';

interface CommentListProps {
  postId: number;
}

interface CommentItemProps {
  comment: TCommentItem;
  level: number;
}

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  level
}) => {
  const getAvatarUrl = () => {
    return comment.author?.image ?? generatePlaceholderImage({ x: 40, y: 40 });
  };

  return (
    <li className="comment">
      <article className="comment-body">
        <footer className="comment-meta">
          <div className="comment-author vcard">
            <Image
              src={getAvatarUrl()}
              className="avatar"
              alt={comment.author?.full_name || 'Anonymous'}
              width={95}
              height={95}
            />
            <b className="fn">{comment.author?.full_name || 'Anonymous'}</b>
            <span className="says">says:</span>
          </div>

          <div className="comment-metadata">
            {comment.author?.role && (
              <span className="author-role">{comment.author.role}</span>
            )}
          </div>
        </footer>

        <div className="comment-content">
          <div dangerouslySetInnerHTML={{ __html: comment.content }} />

          <div className="comment-stats">
            {comment.is_liked && <span className="liked">❤️</span>}
            <span className="likes-count">{comment.likes_count} likes</span>
          </div>
        </div>
      </article>

      {comment.replies && comment.replies.length > 0 && (
        <ol className="children">
          {comment.replies.map((reply) => (
            <CommentItem
              key={reply.id}
              comment={reply}
              level={level + 1}
            />
          ))}
        </ol>
      )}
    </li>
  );
};

const CommentList: React.FC<CommentListProps> = ({ postId }) => {
  const [comments, setComments] = useState<TCommentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await commentApi.getByPost(postId, 1, 10);

      if (response.error) {
        setError(response.message || 'Failed to load comments');
      } else {
        setComments(response.data || []);
        setError(null);
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComments().finally();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [postId]);

  if (loading) {
    return (
      <div className="comments-area">
        <div className="loading">Loading comments...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="comments-area">
        <div className="error">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="comments-area">
      <h3 id="comments" className="comments-title">
        {comments.length} {comments.length === 1 ? 'Comment' : 'Comments'}
      </h3>

      {comments.length > 0 && (
        <ol className="comment-list">
          {comments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              level={0}
            />
          ))}
        </ol>
      )}
    </div>
  );
};

export default CommentList;
