"use client";

import React, { useState, useEffect } from "react";
import * as Icon from "react-feather";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface MenuItem {
  label: string;
  href: string;
  submenu?: MenuItem[];
  isNew?: boolean;
}

const menuData: MenuItem[] = [
  {
    label: "Home",
    href: "#",
    submenu: [
      { label: "IT Startup", href: "/" },
      { label: "IT Startup Two", href: "/it-startup-2/" },
      { label: "IOT", href: "/iot/" },
      { label: "Hosting", href: "/hosting/" },
      { label: "Machine Learning", href: "/machine-learning/" },
      { label: "Machine Learning 2", href: "/machine-learning-2/" },
      { label: "Bigdata Analytics", href: "/bigdata-analytics/" },
      { label: "Digital Agency", href: "/digital-agency/" },
      { label: "Digital Agency Portfolio", href: "/digital-agency-portfolio/" },
      { label: "PC Repair", href: "/pc-repair/" },
      { label: "SaaS", href: "/saas/", isNew: true },
      { label: "FinTech", href: "/fintech/", isNew: true },
      { label: "HealthTech", href: "/health-tech/", isNew: true },
    ],
  },
  {
    label: "About",
    href: "#",
    submenu: [
      { label: "About Style 1", href: "/about/" },
      { label: "About Style 2", href: "/about-2/" },
      { label: "About Style 3", href: "/about-3/" },
    ],
  },
  {
    label: "Pages",
    href: "#",
    submenu: [
      {
        label: "Features",
        href: "#",
        submenu: [
          { label: "Features", href: "/features/" },
          { label: "Features Details", href: "/features/feature-details/" },
        ],
      },
      {
        label: "Services",
        href: "#",
        submenu: [
          { label: "Services Style 1", href: "/services/" },
          { label: "Services Style 2", href: "/services-2/" },
          { label: "Services Style 3", href: "/services-3/" },
          { label: "Services Style 4", href: "/services-4/" },
          { label: "Services Style 5", href: "/services-5/" },
          { label: "Services Details", href: "/services/service-details/" },
        ],
      },
      { label: "Projects", href: "/projects/" },
      {
        label: "Services",
        href: "#",
        submenu: [
          { label: "Project Style 1", href: "/projects/" },
          { label: "Project Style 2", href: "/projects-2/" },
          { label: "Project Details", href: "/projects/project-details/" },
        ],
      },
      { label: "Team", href: "/team/" },
      { label: "Pricing", href: "/pricing/" },
      {
        label: "User",
        href: "#",
        submenu: [
          { label: "Login", href: "/login/" },
          { label: "Sign Up", href: "/sign-up/" },
          { label: "Forgot Password", href: "/forgot-password/" },
        ],
      },
      { label: "FAQ&apos;s", href: "/faq/" },
      { label: "Coming Soon", href: "/coming-soon/" },
      { label: "404 Error Page", href: "/404/" },
    ],
  },
  {
    label: "Shop",
    href: "#",
    submenu: [
      { label: "Shop", href: "/shop/" },
      { label: "Checkout", href: "/checkout/" },
    ],
  },
  {
    label: "Blog",
    href: "#",
    submenu: [
      { label: "Blog Grid", href: "/blog/" },
      { label: "Blog Right Sidebar", href: "/blog-2/" },
      { label: "Blog Grid 2", href: "/blog-3/" },
      { label: "Blog Right Sidebar 2", href: "/blog-4/" },
      { label: "Blog Grid 3", href: "/blog-5/" },
      { label: "Blog Right Sidebar 3", href: "/blog-6/" },
      { label: "Blog Details", href: "/blog/blog-details/" },
    ],
  },
  { label: "Contact", href: "/contact/" },
];

const Navbar: React.FC = () => {
  const currentRoute = usePathname();

  const [menu, setMenu] = useState<boolean>(true);

  const toggleNavbar = () => {
    setMenu(!menu);
  };

  useEffect(() => {
    const handleScroll = () => {
      const header = document.getElementById("header");
      if (window.scrollY > 170) {
        header?.classList.add("is-sticky");
      } else {
        header?.classList.remove("is-sticky");
      }
    };

    document.addEventListener("scroll", handleScroll);
    return () => document.removeEventListener("scroll", handleScroll);
  }, []);

  const renderMenu = (items: MenuItem[]) => {
    return items.map((item, index) => (
      <li className="nav-item" key={index}>
        <Link
          href={item.href}
          onClick={item.href === "#" ? undefined : toggleNavbar}
          className={`nav-link ${item.href === currentRoute ? "active" : ""} ${
            item.submenu ? "dropdown-toggle" : ""
          }`}
        >
          {item.label} {item.isNew && <span className="new">New</span>}
          {item.submenu && <Icon.ChevronDown />}
        </Link>
        {item.submenu && (
          <ul className="dropdown-menu">{renderMenu(item.submenu)}</ul>
        )}
      </li>
    ));
  };

  const classOne = menu
    ? "collapse navbar-collapse"
    : "collapse navbar-collapse show";
  const classTwo = menu
    ? "navbar-toggler navbar-toggler-right collapsed"
    : "navbar-toggler navbar-toggler-right";

  return (
    <header id="header" className="headroom health-tech-navbar">
      <div className="startp-nav">
        <div className="container">
          <nav className="navbar navbar-expand-md navbar-light">
            <Link href="/" className="navbar-brand">
              <Image
                src="/images/saas/bold-logo.png"
                alt="logo"
                width={110}
                height={36}
              />
            </Link>

            <button
              onClick={toggleNavbar}
              className={classTwo}
              type="button"
              data-toggle="collapse"
              data-target="#navbarSupportedContent"
              aria-controls="navbarSupportedContent"
              aria-expanded="false"
              aria-label="Toggle navigation"
            >
              <span className="icon-bar top-bar"></span>
              <span className="icon-bar middle-bar"></span>
              <span className="icon-bar bottom-bar"></span>
            </button>

            <div className={classOne} id="navbarSupportedContent">
              <ul className="navbar-nav mx-auto">{renderMenu(menuData)}</ul>
            </div>

            <div className="others-option">
              <Link href="/cart/" className="cart-wrapper-btn">
                <Icon.ShoppingCart />
                <span>3</span>
              </Link>

              <Link href="/contact/" className="btn btn-light">
                Support
              </Link>

              <Link href="/login/" className="btn btn-primary">
                Login
              </Link>
            </div>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
