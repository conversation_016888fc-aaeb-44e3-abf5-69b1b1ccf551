"use client";

import React from "react";
import Link from "next/link";
import * as Icon from "react-feather";

const OurFeatures = () => {
  return (
    <>
      <div className="features-area pt-80 pb-50 bg-f9f6f6">
        <div className="container">
          <div className="section-title">
            <h2>Our Features</h2>
            <div className="bar"></div>
            <p>
              Discover powerful features that enhance efficiency and user experience.<br />
              Built for performance, scalability, and ease of use.
            </p>
          </div>

          <div className="row">
            <div className="col-lg-6 col-md-6">
              <div className="single-features">
                <div className="icon">
                  <Icon.Settings />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Incredible Infrastructure
                  </Link>
                </h3>
                <p>
                  Designed for speed, stability, and scalability to meet the demands of your growing business.
                </p>
              </div>
            </div>

            <div className="col-lg-6 col-md-6">
              <div className="single-features">
                <div className="icon">
                  <Icon.Mail />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Email Notifications
                  </Link>
                </h3>
                <p>
                  Receive instant alerts and updates through automated email notifications, so you never miss a beat.
                </p>
              </div>
            </div>

            <div className="col-lg-6 col-md-6">
              <div className="single-features">
                <div className="icon bg-c679e3">
                  <Icon.Grid />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Simple Dashboard
                  </Link>
                </h3>
                <p>
                  Manage everything effortlessly with a clean, intuitive dashboard built for clarity and control.
                </p>
              </div>
            </div>

            <div className="col-lg-6 col-md-6">
              <div className="single-features">
                <div className="icon bg-c679e3">
                  <Icon.Info />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Information Retrieval
                  </Link>
                </h3>
                <p>
                  Find, access, and organize critical information quickly — right when you need it most.
                </p>
              </div>
            </div>

            <div className="col-lg-6 col-md-6">
              <div className="single-features">
                <div className="icon bg-eb6b3d">
                  <Icon.Box />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Drag & Drop Functionality
                  </Link>
                </h3>
                <p>
                  Easily customize layouts and content with smart, user-friendly drag-and-drop features.
                </p>
              </div>
            </div>

            <div className="col-lg-6 col-md-6">
              <div className="single-features">
                <div className="icon bg-eb6b3d">
                  <Icon.Bell />
                </div>
                <h3>
                  <Link href="/features/feature-details/" >
                    Deadline Reminders
                  </Link>
                </h3>
                <p>
                  Stay ahead of your tasks with timely reminders and smart notifications that keep you on track.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default OurFeatures;
