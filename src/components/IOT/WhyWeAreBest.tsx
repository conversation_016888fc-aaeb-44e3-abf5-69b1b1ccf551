"use client";

import React from "react";
import Link from "next/link";
import * as Icon from "react-feather";
import Image from "next/image";

// Dynamic Data Array
const reasonsData = [
  {
    id: 1,
    icon: "/images/icon1.png",
    title: "Digital Technology",
    description:
      "Lorem ipsum dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 100,
  },
  {
    id: 2,
    icon: "/images/icon2.png",
    title: "Business Protection",
    description:
      "Lorem ipsum dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 200,
  },
  {
    id: 3,
    icon: "/images/icon3.png",
    title: "Data Analysis",
    description:
      "Lorem ipsum dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 300,
  },
];

const WhyWeAreBest: React.FC = () => {
  return (
    <>
      <div className="iot-why-choose-us pt-80">
        <div className="container">
          <div className="section-title">
            <h2>Why We Are Best From Others</h2>
            <div className="bar"></div>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
          </div>

          <div className="row justify-content-center">
            {reasonsData.map((reason) => (
              <div
                key={reason.id}
                className="col-lg-4 col-md-6"
                data-aos="fade-up"
                data-aos-delay={reason.delay}
                data-aos-duration="700"
              >
                <div className="single-iot-box">
                  <div className="icon">
                    <Image
                      src={reason.icon}
                      alt={`${reason.title} icon`}
                      width={60}
                      height={60}
                    />
                  </div>
                  <h3>{reason.title}</h3>
                  <p>{reason.description}</p>
                  <Link href={reason.link}>
                    <Icon.ArrowRight />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default WhyWeAreBest;
