"use client";

import React from "react";
import * as Icon from "react-feather";
import {
  CSLANT_ADDRESS,
  CSLANT_CONTACT_EMAIL,
  CSLANT_PHONE_DISPLAY,
  CSLANT_PHONE_NUMBER,
  SOCIAL_GOOGLE_MAP_URL
} from "@/constants/app";

const ContactInfo = () => {
  return (
    <>
      <div className="contact-info-area ptb-80">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-4 col-md-6 col-sm-6">
              <div className="contact-info-box">
                <div className="icon">
                  <Icon.Mail />
                </div>
                <h3>Mail Here</h3>
                <p>
                  <a href={'mailto:' + CSLANT_CONTACT_EMAIL}>
                    {CSLANT_CONTACT_EMAIL}
                    </a>
                </p>
              </div>
            </div>

            <div className="col-lg-4 col-md-6 col-sm-6">
              <div className="contact-info-box">
                <div className="icon">
                  <Icon.MapPin />
                </div>
                <h3>Visit Here</h3>
                <p>
                  <a href={SOCIAL_GOOGLE_MAP_URL} target="_blank" rel="noopener noreferrer">
                    {CSLANT_ADDRESS}
                  </a>
                </p>
              </div>
            </div>

            <div className="col-lg-4 col-md-6 col-sm-6">
              <div className="contact-info-box">
                <div className="icon">
                  <Icon.Phone />
                </div>
                <h3>Call Here</h3>
                <p>
                  <a href={'tel:' + CSLANT_PHONE_NUMBER}>
                    {CSLANT_PHONE_DISPLAY}
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContactInfo;
