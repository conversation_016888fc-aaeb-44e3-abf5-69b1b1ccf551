import React from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

const CookiePolicyContent = () => {
  const t = useTranslations('pages.cookie-policy');
  const tCommon = useTranslations('common');

  return (
    <>
      <div id="privacyPolicy" className="main-text-area ptb-80 landing-page-custom">
        <div className="container">
          <h2>{t('cookie-policy1.title')}</h2>
          <p>
            {t('cookie-policy1.content')} <Link
            href={'/'}
            title="cslant.com"
            aria-label="cslant.com"
          >
            cslant.com
          </Link>{' '}{t('cookie-policy1.content1')}
          </p>
          <p className="pre-line">
            {t('cookie-policy1.content2')}
          </p>

          <h2>{t('cookie-policy2.title')}</h2>
          <p className="pre-line">
            {t('cookie-policy2.content')}
          </p>

          <h2>{t('cookie-policy3.title')}</h2>
          <p className="pre-line">
            {t('cookie-policy3.content')}
          </p>
          <ul>
            <li>{t('cookie-policy3.subpoints.subpoint1')}</li>
          </ul>

          <p className="pre-line">
            {t('cookie-policy3.content2')}
          </p>

          <h3>{t('cookie-policy3.subcontent1.title')}</h3>
          <p>
            {t('cookie-policy3.subcontent1.content')}
          </p>

          <h2>{t('cookie-policy4.title')}</h2>
          <p className='pre-line'>
            {t('cookie-policy4.content')}{' '}
            <a
              href={'https://allaboutcookies.org/'}
              title="https://allaboutcookies.org/"
              aria-label="https://allaboutcookies.org/"
            >
              www.allaboutcookies.org
            </a>.
          </p>
          <p>
            {t('more-info')} <Link
            href={'/privacy-policy'}
            title="CSlant Privacy Policy"
            className="text-base font-medium text-primary"
            aria-label="CSlant Privacy Policy"
          >
            {tCommon('privacy-policy')}
          </Link>.
          </p>
        </div>
      </div>
    </>
  );
};

export default CookiePolicyContent;
