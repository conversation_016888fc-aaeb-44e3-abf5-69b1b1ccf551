"use client";

import React from "react";
import Link from "next/link";
import * as Icon from "react-feather";
import { useTranslations } from 'next-intl';
import { formatTitleNormalized } from '@/utils/helper';

const keyAdvantagesData = [
  {
    id: 1,
    icon: <Icon.Settings />,
    title: "Agile & Flexible Approach",
    description:
      "We adapt to changing needs quickly, ensuring your project evolves in the right direction at every stage.",
    iconBg: "",
    delay: 100,
  },
  {
    id: 2,
    icon: <Icon.Mail />,
    title: "Transparent Communication",
    description:
      "Stay updated with clear, consistent communication throughout the entire project lifecycle.",
      iconBg: "",
    delay: 200,
  },
  {
    id: 3,
    icon: <Icon.Grid />,
    title: "Tailored Solutions",
    description:
      "No templates, no one-size-fits-all — every product we build is customized for your unique business goals.",
      iconBg: "bg-c679e3",
    delay: 300,
  },
  {
    id: 4,
    icon: <Icon.Users />,
    title: "Dedicated Small Team",
    description:
      "Work closely with a passionate, focused team that treats your project as their own.",
      iconBg: "bg-c679e3",
    delay: 400,
  },
  {
    id: 5,
    icon: <Icon.Bell />,
    title: "Continuous Support",
    description:
      "Our partnership doesn't end at launch. We offer ongoing maintenance, support, and growth assistance.",
      iconBg: "bg-eb6b3d",
    delay: 500,
  },
  {
    id: 6,
    icon: <Icon.Box />,
    title: "Cost-Effective Excellence",
    description:
      "High-quality development at startup-friendly pricing, balancing affordability with premium execution.",
      iconBg: "bg-eb6b3d",
    delay: 600,
  },
];

const OurKeyAdvantages: React.FC = () => {
  const t = useTranslations('pages.home.advantages');

  return (
    <div className="features-area pt-80 pb-50 bg-f7fafd">
      <div className="container">
        <div className="section-title">
          <h2>{t('title')}</h2>
          <div className="bar"></div>
          <div className="title-detail">
            <p>{t('description')}</p>
          </div>
        </div>

        <div className="row">
          {keyAdvantagesData.map((keyAdvantage) => {
            const titleKey = formatTitleNormalized(keyAdvantage.title);
            return <div
              key={keyAdvantage.id}
              className="col-lg-6 col-md-6"
              data-aos="fade-up"
              data-aos-delay={keyAdvantage.delay}
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className='single-features'>
                <div className={`icon ${keyAdvantage.iconBg}`}>
                  {keyAdvantage.icon}
                </div>
                <h3>
                  <Link href="#">
                    {t(`${titleKey}.title`)}
                  </Link>
                </h3>
                <p>{t(`${titleKey}.description`)}</p>
              </div>
            </div>
          })}
        </div>
      </div>
    </div>
  );
};

export default OurKeyAdvantages;
