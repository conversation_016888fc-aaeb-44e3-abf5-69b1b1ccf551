"use client";

import React from "react";
import <PERSON> from "next/link";
import * as Icon from "react-feather";

const featuresData = [
  {
    id: 1,
    icon: <Icon.Settings />,
    title: "Incredible Infrastructure",
    description:
      "Designed for speed, stability, and scalability to meet the demands of your growing business.",
    iconBg: "",
    delay: 100,
  },
  {
    id: 2,
    icon: <Icon.Mail />,
    title: "Email Notifications",
    description:
      "Receive instant alerts and updates through automated email notifications, so you never miss a beat.",
      iconBg: "",
    delay: 200,
  },
  {
    id: 3,
    icon: <Icon.Grid />,
    title: "Simple Dashboard",
    description:
      "Manage everything effortlessly with a clean, intuitive dashboard built for clarity and control.",
      iconBg: "bg-c679e3",
    delay: 300,
  },
  {
    id: 4,
    icon: <Icon.Info />,
    title: "Information Retrieval",
    description:
      "Find, access, and organize critical information quickly — right when you need it most.",
      iconBg: "bg-c679e3",
    delay: 400,
  },
  {
    id: 5,
    icon: <Icon.Box />,
    title: "Drag & Drop Functionality",
    description:
      "Easily customize layouts and content with smart, user-friendly drag-and-drop features.",
      iconBg: "bg-eb6b3d",
    delay: 500,
  },
  {
    id: 6,
    icon: <Icon.Bell />,
    title: "Deadline Reminders",
    description:
      "Stay ahead of your tasks with timely reminders and smart notifications that keep you on track.",
      iconBg: "bg-eb6b3d",
    delay: 600,
  },
];

const OurFeatures: React.FC = () => {
  return (
    <div className="features-area pt-80 pb-50 bg-f7fafd">
      <div className="container">
        <div className="section-title">
          <h2>Our Features</h2>
          <div className="bar"></div>
          <p>
            Discover powerful features that enhance efficiency and user experience.<br />
            Built for performance, scalability, and ease of use.
          </p>
        </div>

        <div className="row">
          {featuresData.map((feature) => (
            <div
              key={feature.id}
              className="col-lg-6 col-md-6"
              data-aos="fade-up"
              data-aos-delay={feature.delay}
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className='single-features'>
                <div className={`icon ${feature.iconBg}`}>
                  {feature.icon}
                </div>
                <h3>
                  <Link href="/features/feature-details/">
                  {feature.title}
                </Link>
                </h3>
                <p>{feature.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OurFeatures;
