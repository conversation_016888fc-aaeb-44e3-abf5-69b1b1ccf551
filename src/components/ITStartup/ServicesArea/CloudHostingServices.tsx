"use client";

import React from "react";
import * as Icon from "react-feather";
import Image from "next/image";

const servicesData = [
  { id: 1, icon: <Icon.Database />, title: "Cloud databases" },
  { id: 2, icon: <Icon.Globe />, title: "Website hosting" },
  { id: 3, icon: <Icon.File />, title: "File storage" },
  { id: 4, icon: <Icon.TrendingUp />, title: "Forex trading" },
  { id: 5, icon: <Icon.Folder />, title: "File backups" },
  { id: 6, icon: <Icon.Monitor />, title: "Remote desktop" },
  { id: 7, icon: <Icon.Mail />, title: "Email servers" },
  { id: 8, icon: <Icon.Cloud />, title: "Hybrid cloud" },
];

const imagesData = [
  {
    id: 1,
    src: "/images/services-right-image/book-self.png",
    alt: "book-self",
    delay: 100,
    width: 139,
    height: 139,
  },
  {
    id: 2,
    src: "/images/services-right-image/box.png",
    alt: "box",
    delay: 150,
    width: 420,
    height: 251,
  },
  {
    id: 3,
    src: "/images/services-right-image/chair.png",
    alt: "chair",
    delay: 200,
    width: 67,
    height: 127,
  },
  {
    id: 4,
    src: "/images/services-right-image/cloud.png",
    alt: "cloud",
    delay: 250,
    width: 158,
    height: 140,
  },
  {
    id: 5,
    src: "/images/services-right-image/cup.png",
    alt: "cup",
    delay: 300,
    width: 82,
    height: 93,
  },
  {
    id: 6,
    src: "/images/services-right-image/flower-top.png",
    alt: "flower",
    delay: 350,
    width: 27,
    height: 78,
  },
  {
    id: 7,
    src: "/images/services-right-image/head-phone.png",
    alt: "head-phone",
    delay: 400,
    width: 30,
    height: 29,
  },
  {
    id: 8,
    src: "/images/services-right-image/monitor.png",
    alt: "monitor",
    delay: 450,
    width: 70,
    height: 99,
  },
  {
    id: 9,
    src: "/images/services-right-image/mug.png",
    alt: "mug",
    delay: 500,
    width: 16,
    height: 20,
  },
  {
    id: 10,
    src: "/images/services-right-image/table.png",
    alt: "table",
    delay: 550,
    width: 162,
    height: 149,
  },
  {
    id: 11,
    src: "/images/services-right-image/tissue.png",
    alt: "tissue",
    delay: 600,
    width: 26,
    height: 42,
  },
  {
    id: 12,
    src: "/images/services-right-image/water-bottle.png",
    alt: "water-bottle",
    delay: 650,
    width: 14,
    height: 37,
  },
  {
    id: 13,
    src: "/images/services-right-image/wifi.png",
    alt: "wifi",
    delay: 700,
    width: 55,
    height: 71,
  },
  {
    id: 14,
    src: "/images/services-right-image/cercle-shape.png",
    alt: "shape",
    delay: 750,
    width: 524,
    height: 518,
    className: "bg-image rotateme",
  },
  {
    id: 15,
    src: "/images/services-right-image/service-right-main-pic.png",
    alt: "main-pic",
    delay: 800,
    width: 342,
    height: 396,
  },
];

const CloudHostingServices: React.FC = () => {
  return (
    <div className="services-area ptb-80 bg-f7fafd">
      <div className="container">
        <div className="row justify-content-center align-items-center">
          <div className="col-lg-6 col-md-12 services-content">
            <div className="section-title">
              <h2>Cloud Hosting Services</h2>
              <div className="bar"></div>
              <p>
                Reliable cloud solutions tailored for speed, security, and scalability.
              </p>
            </div>

            <div className="row">
              {servicesData.map((service) => (
                <div key={service.id} className="col-lg-6 col-md-6">
                  <div className="box">
                    {service.icon} {service.title}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="col-lg-6 col-md-12 services-right-image">
            {imagesData.map((image) => (
              <Image
                key={image.id}
                src={image.src}
                alt={image.alt}
                data-aos="fade-up"
                data-aos-delay={image.delay}
                data-aos-duration="700"
                data-aos-once="true"
                width={image.width}
                height={image.height}
                className={image.className || ""}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CloudHostingServices;
