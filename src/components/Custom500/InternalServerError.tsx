"use client";
import { assets, effectVariants } from '@/utils/helper';
import EffectWrapper from '@/components/EffectWrapper';
import Link from "next/link";
import "../../../styles/pages/InternalServerError.css";
import Image from "next/image";

const InternalServerError = () => {
  return (
    <div className="internal-server-error">
      <div className="internal-server-error__container">
        <EffectWrapper
          variants={effectVariants(1)}
          delayTime={0.1}
          effectTime={1.4}
        >
          <div className="internal-server-error__icon-wrapper">
            <div className="internal-server-error__icon">
              <Image src={assets('images/service/server-error-icon.svg')} alt="" width={1120} height={700} />
            </div>
          </div>
        </EffectWrapper>
        <EffectWrapper
          variants={effectVariants(4)}
          delayTime={0.1}
          effectTime={1.4}
        >
          <h1 className="internal-server-error__title">
            Something went wrong!
          </h1>
        </EffectWrapper>

        <EffectWrapper
          variants={effectVariants(4)}
          delayTime={0.3}
          effectTime={1.5}
        >
          <h5 className="internal-server-error__subtitle">
            Server Error 500. Our staff has been notified, thank you for your understanding.
          </h5>
        </EffectWrapper>

        <div className="internal-server-error__button-wrapper">
          <Link
            className="internal-server-error__button"
            href="/"
          >
            Back to HomePage
          </Link>
        </div>
      </div>
    </div>
  );
};

export default InternalServerError;
