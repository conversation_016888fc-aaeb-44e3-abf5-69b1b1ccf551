import React, { JSX } from "react";
import * as Icon from "react-feather";
import Image from "next/image";
import { teamData } from "@/api/team";

const TeamMembers = () => {
  const data = teamData;

  const socialIcons: Record<string, JSX.Element> = {
    facebook: <Icon.Facebook />,
    twitter: <Icon.Twitter />,
    linkedin: <Icon.Linkedin />,
    youtube: <Icon.Youtube />,
    instagram: <Icon.Instagram />,
    website: <Icon.Globe />,
    github: <Icon.GitHub />,
    email: <Icon.Mail />,
  }

  const getSocialIcon = (type: string) => {
    return socialIcons[type] || <Icon.Globe />;
  };

  return (
    <>
      <div className="team-area pt-80 pb-50 bg-f9f6f6">
        <div className="container">
          <div className="row justify-content-center">
            {data.map((member, index) => (
              <>
                <div className="col-lg-4 col-md-6">
                  <div className="single-team">
                    <div className="team-image">
                      <Image
                        title={member.sub_description ?? ""}
                        src={member.image}
                        alt={member.name}
                        width={125}
                        height={125}
                      />
                    </div>

                    <div className="team-content">
                      <div className="team-info">
                        <h3>{member.name}</h3>
                        <span>{member.position}</span>
                      </div>

                      <ul>
                        {Object.entries(member.socialLinks).map(([key, link]) => (
                          <li key={key}>
                            <a
                              href={link.url}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {getSocialIcon(link.type)}
                            </a>
                          </li>
                        ))}
                      </ul>

                      <p title={member.sub_description}>{member.description}</p>
                    </div>
                  </div>
                </div>
              </>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default TeamMembers;
