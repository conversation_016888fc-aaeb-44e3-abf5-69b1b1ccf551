"use client";

import React from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    position: "CEO at Envato",
    image: "/images/client-image/client1.jpg",
    feedback:
      "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Aliquid ullam harum sit. Accusantium veritatis dolore ducimus illum, cumque excepturi, autem rerum illo amet placeat odit corporis!",
    rating: 5,
  },
  {
    id: 2,
    name: "<PERSON>",
    position: "CEO at Envato",
    image: "/images/client-image/client2.jpg",
    feedback:
      "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Aliquid ullam harum sit. Accusantium veritatis dolore ducimus illum, cumque excepturi, autem rerum illo amet placeat odit corporis!",
    rating: 5,
  },
  {
    id: 3,
    name: "<PERSON>",
    position: "CEO at Envato",
    image: "/images/client-image/client3.jpg",
    feedback:
      "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Aliquid ullam harum sit. Accusantium veritatis dolore ducimus illum, cumque excepturi, autem rerum illo amet placeat odit corporis!",
    rating: 5,
  },
];

const FeedbackStyleThree = () => {
  return (
    <div className="ml-feedback-area ptb-80">
      <div className="container">
        <div className="section-title st-fs-28">
          <span className="sub-title">Testimonials</span>
          <h2>Our Clients Feedback</h2>
          <div className="bar"></div>
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
        </div>

        <Swiper
          pagination={{
            clickable: true,
          }}
          autoplay={{
            delay: 6000,
            pauseOnMouseEnter: true,
          }}
          modules={[Pagination, Autoplay]}
          className="ml-feedback-slides"
        >
          {testimonials.map((testimonial) => (
            <SwiperSlide key={testimonial.id}>
              <div className="single-testimonials-item">
                <p>{testimonial.feedback}</p>
                <div className="client-info">
                  <Image
                    src={testimonial.image}
                    alt={testimonial.name}
                    width={95}
                    height={95}
                  />
                  <div className="rating d-flex align-items-center gap-1">
                    {Array.from({ length: testimonial.rating }, (_, i) => (
                      <i
                        key={i}
                        className="bx bxs-star"
                        style={{ color: "#ff612f", fontSize: "16px" }}
                      ></i>
                    ))}
                  </div>
                  <h3>{testimonial.name}</h3>
                  <span>{testimonial.position}</span>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* Shape Images */}
      <div className="user-shape1">
        <Image
          src="/images/agency-image/agency-user1.png"
          alt="shape"
          width={131}
          height={143}
        />
      </div>
      <div className="user-shape2">
        <Image
          src="/images/agency-image/agency-user2.png"
          alt="shape"
          width={131}
          height={143}
        />
      </div>
      <div className="user-shape3">
        <Image
          src="/images/agency-image/agency-user3.png"
          alt="shape"
          width={131}
          height={143}
        />
      </div>
      <div className="user-shape4">
        <Image
          src="/images/agency-image/agency-user4.png"
          alt="shape"
          width={131}
          height={143}
        />
      </div>
      <div className="user-shape5">
        <Image
          src="/images/agency-image/agency-user5.png"
          alt="shape"
          width={131}
          height={143}
        />
      </div>
      <div className="user-shape6">
        <Image
          src="/images/agency-image/agency-user6.png"
          alt="shape"
          width={131}
          height={143}
        />
      </div>
    </div>
  );
};

export default FeedbackStyleThree;
