"use client";

import React from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";

import { assets } from '@/utils/helper';
import { SHAPE_IMAGE, SHAPE_SVG } from '@/constants/asset';

const feedbackData = [
  {
    id: 1,
    name: "<PERSON>",
    position: "CEO at Envato",
    image: "/images/client-image/client1.jpg",
    feedback:
      "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Aliquid ullam harum sit. Accusantium veritatis dolore ducimus illum, cumque excepturi, autem rerum illo amet placeat odit corporis!",
    rating: 5,
  },
  {
    id: 2,
    name: "<PERSON>",
    position: "CEO at Envato",
    image: "/images/client-image/client2.jpg",
    feedback:
      "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Aliquid ullam harum sit. Accusantium veritatis dolore ducimus illum, cumque excepturi, autem rerum illo amet placeat odit corporis!",
    rating: 3,
  },
  {
    id: 3,
    name: "<PERSON>",
    position: "CEO at Envato",
    image: "/images/client-image/client3.jpg",
    feedback:
      "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Aliquid ullam harum sit. Accusantium veritatis dolore ducimus illum, cumque excepturi, autem rerum illo amet placeat odit corporis!",
    rating: 3,
  },
];

const FeedbackStyleFour = () => {
  return (
    <div className="ml-feedback-area ptb-80">
      <div className="container">
        <div className="section-title">
          <h2>Our Clients Feedback</h2>
          <div className="bar"></div>
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
        </div>

        <Swiper
          pagination={{
            clickable: true,
          }}
          autoplay={{
            delay: 6000,
            pauseOnMouseEnter: true,
          }}
          modules={[Pagination, Autoplay]}
          className="ml-feedback-slides"
        >
          {feedbackData.map((client) => (
            <SwiperSlide key={client.id}>
              <div className="single-ml-feedback-item">
                <div className="client-info">
                  <Image
                    src={client.image}
                    alt={client.name}
                    width={95}
                    height={95}
                  />
                  <h3>{client.name}</h3>
                  <span>{client.position}</span>
                </div>
                <p>{client.feedback}</p>
                <div className="rating d-flex align-items-center gap-1">
                  {Array.from({ length: client.rating }, (_, i) => (
                    <i
                      key={i}
                      className="bx bxs-star"
                      style={{ color: "#ff612f", fontSize: "16px" }}
                    ></i>
                  ))}
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* Shape Images */}
      <div className="shape1">
        <Image src={assets(SHAPE_IMAGE(1))} alt="shape" width={202} height={202} />
      </div>
      <div className="shape2 rotateme">
        <Image src={assets(SHAPE_SVG(2))} alt="shape" width={22} height={22} />
      </div>
      <div className="shape3">
        <Image src={assets(SHAPE_SVG(3))} alt="shape" width={28} height={28} />
      </div>
      <div className="shape4">
        <Image src={assets(SHAPE_SVG(4))} alt="shape" width={21} height={20} />
      </div>
      <div className="shape7">
        <Image src={assets(SHAPE_SVG(4))} alt="shape" width={21} height={20} />
      </div>
      <div className="shape8 rotateme">
        <Image src={assets(SHAPE_SVG(2))} alt="shape" width={22} height={22} />
      </div>
    </div>
  );
};

export default FeedbackStyleFour;
