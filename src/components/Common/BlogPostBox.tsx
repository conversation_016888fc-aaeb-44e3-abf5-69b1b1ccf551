import React from "react";
import Link from "next/link";
import * as Icon from "react-feather";
import Image from "next/image";
import { TBlogPost } from "@/types/blog";
import { blogSlug } from "@/utils/blog";
import { assets, formatDate } from "@/utils/helper";

type BlogPostProps = {
  post: TBlogPost;
}
const BlogPostBox: React.FC<BlogPostProps> = ({ ...props }: BlogPostProps) => {
  const { post } = props;
  const {
    name = post.name || 'Blog Post',
    slug,
    image,
    description,
    author,
    updated_at
  } = post;
  const authorName: string = author?.full_name || 'Base Author';
  return (
    <div
      key={post.id}
      className="col-lg-4 col-md-6 w-100"
      data-aos="fade-up"
      data-aos-delay={100} // Adjust delay dynamically if needed
      data-aos-duration="700"
      data-aos-once="true"
    >
      <div className="single-blog-post">
        <div className="blog-image">
          <Link href={blogSlug(slug)}>
            <Image
              src={assets(image)}
              alt={name}
              className="blog-post-image"
              width={600}
              height={400}
            />
          </Link>
          <div className="date">
            <Icon.Calendar /> {formatDate(updated_at)}
          </div>
        </div>

        <div className="blog-post-content">
          <h3 title={name} className="text-truncate-title">
            <Link href={blogSlug(slug)}>{name}</Link>
          </h3>
          <span>
            By <Link href={blogSlug(slug)}>{authorName}</Link>
          </span>
          <div className="text-truncate-content">
            <p>{description}</p>
          </div>
          <Link href={blogSlug(slug)} className="read-more-btn">
            Read More <Icon.ArrowRight />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default BlogPostBox;
