"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";

const blogPosts = [
  {
    id: 1,
    title: "The security risks of changing package owners",
    author: "Admin",
    date: "20 February 2020",
    image: "/images/blog-image/blog1.jpg",
    link: "/blog/blog-details/",
  },
  {
    id: 2,
    title: "Tips to Protecting Your Business and Family",
    author: "Admin",
    date: "21 February 2020",
    image: "/images/blog-image/blog2.jpg",
    link: "/blog/blog-details/",
  },
  {
    id: 3,
    title: "Protect Your Workplace from Cyber Attacks",
    author: "Admin",
    date: "22 February 2020",
    image: "/images/blog-image/blog3.jpg",
    link: "/blog/blog-details/",
  },
  {
    id: 4,
    title: "Four New WordPress.com Color Schemes",
    author: "Admin",
    date: "22 February 2020",
    image: "/images/blog-image/blog4.jpg",
    link: "/blog/blog-details/",
  },
];

const BlogPostStyleThree = () => {
  return (
    <div className="blog-area ptb-80">
      <div className="container">
        <div className="section-title text-left">
          <h2>
            Our Recent <span>News</span>
          </h2>
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
        </div>

        <Swiper
          spaceBetween={30}
          pagination={{
            clickable: true,
          }}
          autoplay={{
            delay: 6000,
            pauseOnMouseEnter: true,
          }}
          breakpoints={{
            0: {
              slidesPerView: 1,
            },
            576: {
              slidesPerView: 2,
            },
            1024: {
              slidesPerView: 3,
            },
          }}
          modules={[Pagination, Autoplay]}
          className="blog-slides"
        >
          {blogPosts.slice(0, 10).map((post) => (
            <SwiperSlide key={post.id}>
              <div className="single-blog-item">
                <div className="post-image">
                  <Link href={post.link}>
                    <Image
                      src={post.image}
                      alt={post.title}
                      width={860}
                      height={700}
                    />
                  </Link>
                </div>

                <div className="post-content">
                  <ul className="post-meta">
                    <li>
                      By{" "}
                      <Link href="#">{post.author}</Link>
                    </li>
                    <li>{post.date}</li>
                  </ul>
                  <h3>
                    <Link href={post.link}>{post.title}</Link>
                  </h3>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default BlogPostStyleThree;
