"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import * as Icon from "react-feather";

import shape2 from "/public/images/shape2.svg";
import shape3 from "/public/images/shape3.svg";
import shape4 from "/public/images/shape4.svg";

const blogPosts = [
  {
    id: 1,
    title: "The security risks of changing package owners",
    author: "Admin",
    date: "August 15, 2024",
    image: "/images/blog-image/blog1.jpg",
    link: "/blog/blog-details/",
    delay: 100,
  },
  {
    id: 2,
    title: "Tips to Protecting Your Business and Family",
    author: "Admin",
    date: "August 15, 2024",
    image: "/images/blog-image/blog2.jpg",
    link: "/blog/blog-details/",
    delay: 200,
  },
  {
    id: 3,
    title: "Protect Your Workplace from Cyber Attacks",
    author: "Admin",
    date: "August 15, 2024",
    image: "/images/blog-image/blog3.jpg",
    link: "/blog/blog-details/",
    delay: 300,
  },
];

const BlogPostStyleTwo = () => {
  return (
    <div className="blog-area pt-80 pb-50">
      <div className="container">
        <div className="section-title st-fs-28">
          <span className="sub-title">News</span>
          <h2>The News from Our Blog</h2>
          <div className="bar"></div>
          <p>
            Stay updated with the latest insights, tips, and trends.<br />
            Explore our blog for expert advice and industry news.
          </p>
        </div>

        <div className="row justify-content-center">
          {blogPosts.slice(0, 3).map((post) => (
            <div
              key={post.id}
              className="col-lg-4 col-md-6"
              data-aos="fade-up"
              data-aos-delay={post.delay}
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className="single-blog-post-item">
                <div className="post-image">
                  <Link href={post.link}>
                    <Image
                      src={post.image}
                      alt={post.title}
                      width={860}
                      height={700}
                    />
                  </Link>
                </div>

                <div className="post-content">
                  <ul className="post-meta">
                    <li>
                      <Link href="#">{post.author}</Link>
                    </li>
                    <li>{post.date}</li>
                  </ul>
                  <h3>
                    <Link href={post.link}>{post.title}</Link>
                  </h3>
                  <Link href={post.link} className="read-more-btn">
                    Read More <Icon.PlusCircle />
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Shape Images */}
      <div className="shape2 rotateme">
        <Image src={shape2} alt="shape" width={22} height={22} />
      </div>
      <div className="shape3">
        <Image src={shape3} alt="shape" width={28} height={28} />
      </div>
      <div className="shape4">
        <Image src={shape4} alt="shape" width={21} height={20} />
      </div>
      <div className="shape6 rotateme">
        <Image src={shape4} alt="shape" width={21} height={20} />
      </div>
      <div className="shape7">
        <Image src={shape4} alt="shape" width={21} height={20} />
      </div>
      <div className="shape8 rotateme">
        <Image src={shape2} alt="shape" width={22} height={22} />
      </div>
    </div>
  );
};

export default BlogPostStyleTwo;
