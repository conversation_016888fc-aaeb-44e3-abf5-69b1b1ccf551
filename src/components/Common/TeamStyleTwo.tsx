"use client";

import React from "react";
import * as Icon from "react-feather";
import Image from "next/image";

import teamImg1 from "/public/images/team-image/team1.jpg";
import teamImg2 from "/public/images/team-image/team2.jpg";
import teamImg3 from "/public/images/team-image/team3.jpg";
import teamImg4 from "/public/images/team-image/team4.jpg";

const teamData = [
  {
    id: 1,
    name: "<PERSON>",
    position: "CEO & Founder",
    image: "/images/team-image/team1.jpg",
    description:
      "Risus commodo viverra maecenas accumsan lacus vel facilisis quis ipsum.",
    aosDelay: "100",
    socialLinks: [
      {
        id: 1,
        url: "https://www.facebook.com/",
        icon: <Icon.Facebook />,
      },
      {
        id: 2,
        url: "https://www.twitter.com/",
        icon: <Icon.Twitter />,
      },
      {
        id: 3,
        url: "https://www.linkedin.com/",
        icon: <Icon.Linkedin />,
      },
      {
        id: 4,
        url: "https://www.gitlab.com/",
        icon: <Icon.Gitlab />,
      },
    ],
  },
  {
    id: 2,
    name: "Alex <PERSON>wel",
    position: "Marketing Manager",
    image: "/images/team-image/team2.jpg",
    description:
      "Risus commodo viverra maecenas accumsan lacus vel facilisis quis ipsum.",
    aosDelay: "200",
    socialLinks: [
      {
        id: 1,
        url: "https://www.facebook.com/",
        icon: <Icon.Facebook />,
      },
      {
        id: 2,
        url: "https://www.twitter.com/",
        icon: <Icon.Twitter />,
      },
      {
        id: 3,
        url: "https://www.linkedin.com/",
        icon: <Icon.Linkedin />,
      },
      {
        id: 4,
        url: "https://www.gitlab.com/",
        icon: <Icon.Gitlab />,
      },
    ],
  },
  {
    id: 3,
    name: "Janny Cotller",
    position: "Web Developer",
    image: "/images/team-image/team3.jpg",
    description:
      "Risus commodo viverra maecenas accumsan lacus vel facilisis quis ipsum.",
    aosDelay: "300",
    socialLinks: [
      {
        id: 1,
        url: "https://www.facebook.com/",
        icon: <Icon.Facebook />,
      },
      {
        id: 2,
        url: "https://www.twitter.com/",
        icon: <Icon.Twitter />,
      },
      {
        id: 3,
        url: "https://www.linkedin.com/",
        icon: <Icon.Linkedin />,
      },
      {
        id: 4,
        url: "https://www.gitlab.com/",
        icon: <Icon.Gitlab />,
      },
    ],
  },
  {
    id: 4,
    name: "Jason Statham",
    position: "UX/UI Designer",
    image: "/images/team-image/team4.jpg",
    description:
      "Risus commodo viverra maecenas accumsan lacus vel facilisis quis ipsum.",
    aosDelay: "400",
    socialLinks: [
      {
        id: 1,
        url: "https://www.facebook.com/",
        icon: <Icon.Facebook />,
      },
      {
        id: 2,
        url: "https://www.twitter.com/",
        icon: <Icon.Twitter />,
      },
      {
        id: 3,
        url: "https://www.linkedin.com/",
        icon: <Icon.Linkedin />,
      },
      {
        id: 4,
        url: "https://www.gitlab.com/",
        icon: <Icon.Gitlab />,
      },
    ],
  },
];

const TeamStyleTwo = () => {
  return (
    <>
      <div className="team-area repair-team-area pt-80 pb-50 bg-f9f6f6">
        <div className="container">
          <div className="section-title">
            <h2>Our Awesome Team</h2>
            <div className="bar"></div>
            <p>
              Meet the passionate experts behind our success.<br />
              Driven by innovation, collaboration, and excellence.
            </p>
          </div>

          <div className="row justify-content-center">
            {teamData.map((member) => (
              <div
                className="col-lg-3 col-md-6"
                data-aos="fade-up"
                data-aos-delay={member.aosDelay}
                data-aos-duration="700"
                data-aos-once="true"
                key={member.id}
              >
                <div className="single-team">
                  <div className="team-image">
                    <Image
                      src={member.image}
                      alt={member.name}
                      width={125}
                      height={125}
                    />
                  </div>

                  <div className="team-content">
                    <div className="team-info">
                      <h3>{member.name}</h3>
                      <span>{member.position}</span>
                    </div>

                    <ul>
                      {Object.entries(member.socialLinks).map(([key, link]) => (
                        <li key={key}>
                          <a
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {link.icon}
                          </a>
                        </li>
                      ))}
                    </ul>

                    <p>{member.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default TeamStyleTwo;
