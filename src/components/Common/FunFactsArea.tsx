"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { assets, formatTitleNormalized } from '@/utils/helper';
import { useTranslations } from 'next-intl';

const funFactsData = [
  { id: 1, value: "20+", description: "Projects delivered", delay: 100 },
  { id: 2, value: "5+", description: "Years of experience average", delay: 200 },
  { id: 3, value: "100%", description: "Customer satisfaction focus", delay: 300 },
  { id: 4, value: "∞", description: "Possibilities to grow with us", delay: 400 },
];

const FunFactsArea = () => {
  const t = useTranslations('pages.home.fact');
  const tContact = useTranslations('pages.home.contactZone');

  return (
    <div className="funfacts-area ptb-80 funfacts-cslant-area">
      <div className="container">
        <div
          className="section-title"
          data-aos="fade-up"
          data-aos-delay="100"
          data-aos-duration="700"
          data-aos-once="true"
        >
          <h2>{t('title')}</h2>
          <div className="bar"></div>
          <p>{t('description')}</p>
        </div>

        <div className="row">
          {funFactsData.map((fact) => {
            const descriptionKey = formatTitleNormalized(fact.description);
            return <div
              key={fact.id}
              className="col-lg-3 col-md-3 col-6"
              data-aos="fade-up"
              data-aos-delay={fact.delay}
              data-aos-duration="700"
              data-aos-once="true"
            >
              <div className="funfact">
                <h3>{fact.value}</h3>
                <p>{t(`${descriptionKey}`)}</p>
              </div>
            </div>
          })}
        </div>

        <div
          className="contact-cta-box"
          data-aos="fade-up"
          data-aos-delay="500"
          data-aos-duration="700"
          data-aos-once="true"
        >
          <h3>{tContact('title')}</h3>
          <p>{tContact('subtitle')}</p>

          <Link href="/contact" className="btn btn-primary"> {tContact('contactUs')} </Link>
        </div>

        <div className="map-bg">
          <Image src={assets('v2/images/map.png')} alt="map" width={910} height={443} />
        </div>
      </div>
      <div className="area-absolute">
        <Image
          src={assets('/images/intro/area-1.svg')}
          alt="shape"
          width={364}
          height={201}
        />
      </div>
    </div>
  );
};

export default FunFactsArea;
