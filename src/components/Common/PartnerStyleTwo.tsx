"use client";

import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import Image from "next/image";

const partners = [
  {
    id: 1,
    image: "/images/partner-img/partner-1.png",
    hoverImage: "/images/partner-img/partner-hover1.png",
    width: 103,
    height: 33,
  },
  {
    id: 2,
    image: "/images/partner-img/partner-2.png",
    hoverImage: "/images/partner-img/partner-hover2.png",
    width: 100,
    height: 29,
  },
  {
    id: 3,
    image: "/images/partner-img/partner-3.png",
    hoverImage: "/images/partner-img/partner-hover3.png",
    width: 114,
    height: 22,
  },
  {
    id: 4,
    image: "/images/partner-img/partner-4.png",
    hoverImage: "/images/partner-img/partner-hover4.png",
    width: 137,
    height: 25,
  },
  {
    id: 5,
    image: "/images/partner-img/partner-5.png",
    hoverImage: "/images/partner-img/partner-hover5.png",
    width: 89,
    height: 24,
  },
  {
    id: 6,
    image: "/images/partner-img/partner-6.png",
    hoverImage: "/images/partner-img/partner-hover6.png",
    width: 120,
    height: 33,
  },
  {
    id: 7,
    image: "/images/partner-img/partner-7.png",
    hoverImage: "/images/partner-img/partner-hover7.png",
    width: 95,
    height: 33,
  },
];

const PartnerStyleTwo = () => {
  return (
    <div className="repair-partner-area bg-f9fafb">
      <div className="container">
        <Swiper
          spaceBetween={30}
          autoplay={{
            delay: 6000,
            pauseOnMouseEnter: true,
          }}
          breakpoints={{
            0: {
              slidesPerView: 2,
            },
            576: {
              slidesPerView: 3,
            },
            768: {
              slidesPerView: 4,
            },
            1024: {
              slidesPerView: 5,
            },
            1200: {
              slidesPerView: 6,
            },
          }}
          modules={[Autoplay]}
          className="repair-partner-slides"
        >
          {partners.map((partner) => (
            <SwiperSlide key={partner.id}>
              <div className="single-repair-partner">
                <a href="#" target="_blank">
                  <Image
                    src={partner.image}
                    alt={`Partner ${partner.id}`}
                    width={partner.width}
                    height={partner.height}
                  />
                  <Image
                    src={partner.hoverImage}
                    alt={`Partner ${partner.id}`}
                    width={partner.width}
                    height={partner.height}
                  />
                </a>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default PartnerStyleTwo;
