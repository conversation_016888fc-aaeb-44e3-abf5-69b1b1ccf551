"use client";

import React from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";

const feedbackData = [
  {
    id: 1,
    image: "/images/client-image/client1.jpg",
    feedback:
      "Pellentesque sed purus eu urna vulputate interdum quis sit amet sapien. Pellentesque porta mauris at orci sagittis commodo. Curabitur aliquam nibh odio, vel ornare nisl volutpat quis. Maecenas congue dapibus lacus id fringilla. Vestibulum id augue massa. Proin sed neque dapibus, vulputate ligula eget, aliquam eros.",
  },
  {
    id: 2,
    image: "/images/client-image/client2.jpg",
    feedback:
      "Pellentesque sed purus eu urna vulputate interdum quis sit amet sapien. Pellentesque porta mauris at orci sagittis commodo. Curabitur aliquam nibh odio, vel ornare nisl volutpat quis. Maecenas congue dapibus lacus id fringilla. Vestibulum id augue massa. Proin sed neque dapibus, vulputate ligula eget, aliquam eros.",
  },
  {
    id: 3,
    image: "/images/client-image/client3.jpg",
    feedback:
      "Pellentesque sed purus eu urna vulputate interdum quis sit amet sapien. Pellentesque porta mauris at orci sagittis commodo. Curabitur aliquam nibh odio, vel ornare nisl volutpat quis. Maecenas congue dapibus lacus id fringilla. Vestibulum id augue massa. Proin sed neque dapibus, vulputate ligula eget, aliquam eros.",
  },
];

const FeedbackStyleFive = () => {
  return (
    <div className="agency-portfolio-feedback-area ptb-80">
      <div className="container">
        <Swiper
          pagination={{
            clickable: true,
          }}
          autoplay={{
            delay: 6000,
            pauseOnMouseEnter: true,
          }}
          modules={[Pagination, Autoplay]}
          className="agency-portfolio-feedback-slides"
        >
          {feedbackData.map((item) => (
            <SwiperSlide key={item.id}>
              <div className="agency-portfolio-feedback-item">
                <Image
                  src={item.image}
                  alt="client feedback"
                  width={95}
                  height={95}
                />
                <p>{item.feedback}</p>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default FeedbackStyleFive;
