'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';

interface PaginationProps {
  currentPage: number;
  lastPage: number;
  total?: number;
  perPage?: number;
  from?: number;
  to?: number;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  lastPage
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createPageURL = (pageNumber: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', pageNumber.toString());
    return `${pathname}?${params.toString()}`;
  };

  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (
      let i = Math.max(2, currentPage - delta);
      i <= Math.min(lastPage - 1, currentPage + delta);
      i++
    ) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < lastPage - 1) {
      rangeWithDots.push('...', lastPage);
    } else if (lastPage > 1) {
      rangeWithDots.push(lastPage);
    }

    return rangeWithDots;
  };

  if (lastPage <= 1) {
    return null;
  }

  const visiblePages = getVisiblePages();

  return (
    <div className="col-lg-12 col-md-12">
      <div className="pagination-area">
        <nav aria-label="Page navigation">
          <ul className="pagination justify-content-center">
            {/* Previous Button */}
            <li className="page-item">
              {currentPage === 1 ? (
                <span className="page-link">Prev</span>
              ) : (
                <Link href={createPageURL(currentPage - 1)} className="page-link">
                  Prev
                </Link>
              )}
            </li>

            {/* Page Numbers */}
            {visiblePages.map((page, index) => (
              <li key={index} className={`page-item ${page === currentPage ? 'active' : ''}`}>
                {page === '...' ? (
                  <span className="page-link">...</span>
                ) : (
                  <Link
                    href={createPageURL(page as number)}
                    className="page-link"
                  >
                    {page}
                  </Link>
                )}
              </li>
            ))}

            {/* Next Button */}
            <li className="page-item">
              {currentPage === lastPage ? (
                <span className="page-link">Next</span>
              ) : (
                <Link href={createPageURL(currentPage + 1)} className="page-link">
                  Next
                </Link>
              )}
            </li>
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default Pagination;