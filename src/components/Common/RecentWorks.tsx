"use client";

import React from "react";
import Link from "next/link";
import * as Icon from "react-feather";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";

const recentWorksData = [
  {
    id: 1,
    image: "/images/works-image/works-image1.jpg",
    title: "Incredible Infrastructure",
    description:
      "Designed for speed, stability, and scalability to meet the demands of your growing business.",
    link: "/projects/project-details/",
    icon: <Icon.Settings />,
  },
  {
    id: 2,
    image: "/images/works-image/works-image2.jpg",
    title: "Email Notifications",
    description:
      "Receive instant alerts and updates through automated email notifications, so you never miss a beat.",
    link: "/projects/project-details/",
    icon: <Icon.Mail />,
  },
  {
    id: 3,
    image: "/images/works-image/works-image3.jpg",
    title: "Best Analytics Audits",
    description:
      "Manage everything effortlessly with a clean, intuitive dashboard built for clarity and control.",
    link: "/projects/project-details/",
    icon: <Icon.BarChart />,
  },
  {
    id: 4,
    image: "/images/works-image/works-image4.jpg",
    title: "Simple Dashboard",
    description:
      "Find, access, and organize critical information quickly — right when you need it most.",
    link: "/projects/project-details/",
    icon: <Icon.Grid />,
  },
  {
    id: 5,
    image: "/images/works-image/works-image5.jpg",
    title: "Information Retrieval",
    description:
      "Easily customize layouts and content with smart, user-friendly drag-and-drop features.",
    link: "/projects/project-details/",
    icon: <Icon.Info />,
  },
];

const RecentWorks = () => {
  return (
    <div className="works-area pt-80 pb-50 bg-f7fafd">
      <div className="container">
        <div className="section-title">
          <h2>Our Recent Works</h2>
          <div className="bar"></div>
          <p>
            Explore a showcase of our latest creative and technical achievements.<br />
            Each project reflects our commitment to quality and innovation.
          </p>
        </div>
      </div>

      <Swiper
        spaceBetween={30}
        pagination={{
          clickable: true,
        }}
        autoplay={{
          delay: 6000,
          pauseOnMouseEnter: true,
        }}
        breakpoints={{
          0: {
            slidesPerView: 1,
          },
          576: {
            slidesPerView: 2,
          },
          1024: {
            slidesPerView: 3,
          },
          1200: {
            slidesPerView: 4,
          },
        }}
        modules={[Pagination, Autoplay]}
        className="works-slides"
      >
        {recentWorksData.map((work) => (
          <SwiperSlide key={work.id}>
            <div className="single-works">
              <Image
                src={work.image}
                alt={work.title}
                width={640}
                height={450}
              />

              <Link href={work.link} className="icon">
                {work.icon}
              </Link>

              <div className="works-content">
                <h3>
                  <Link href={work.link}>{work.title}</Link>
                </h3>
                <p>{work.description}</p>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Shape Images */}
      <div className="shape8 rotateme">
        <Image src="/images/shape2.svg" alt="shape" width={22} height={22} />
      </div>
      <div className="shape2 rotateme">
        <Image src="/images/shape2.svg" alt="shape" width={22} height={22} />
      </div>
      <div className="shape7">
        <Image src="/images/shape4.svg" alt="shape" width={21} height={20} />
      </div>
      <div className="shape4">
        <Image src="/images/shape4.svg" alt="shape" width={21} height={20} />
      </div>
    </div>
  );
};

export default RecentWorks;
