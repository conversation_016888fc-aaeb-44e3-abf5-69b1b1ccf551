"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";

const partnerData = [
  {
    id: 1,
    defaultImage: "/images/partner-img/partner-2.png",
    hoverImage: "/images/partner-img/partner-hover2.png",
    width: 100,
    height: 29,
  },
  {
    id: 2,
    defaultImage: "/images/partner-img/partner-3.png",
    hoverImage: "/images/partner-img/partner-hover3.png",
    width: 114,
    height: 22,
  },
  {
    id: 3,
    defaultImage: "/images/partner-img/partner-4.png",
    hoverImage: "/images/partner-img/partner-hover4.png",
    width: 137,
    height: 25,
  },
  {
    id: 4,
    defaultImage: "/images/partner-img/partner-5.png",
    hoverImage: "/images/partner-img/partner-hover5.png",
    width: 89,
    height: 24,
  },
  {
    id: 5,
    defaultImage: "/images/partner-img/partner-6.png",
    hoverImage: "/images/partner-img/partner-hover6.png",
    width: 120,
    height: 33,
  },
  {
    id: 6,
    defaultImage: "/images/partner-img/partner-7.png",
    hoverImage: "/images/partner-img/partner-hover7.png",
    width: 95,
    height: 33,
  },
  {
    id: 7,
    defaultImage: "/images/partner-img/partner-6.png",
    hoverImage: "/images/partner-img/partner-hover6.png",
    width: 120,
    height: 33,
  },
  {
    id: 8,
    defaultImage: "/images/partner-img/partner-2.png",
    hoverImage: "/images/partner-img/partner-hover2.png",
    width: 100,
    height: 29,
  },
  {
    id: 9,
    defaultImage: "/images/partner-img/partner-7.png",
    hoverImage: "/images/partner-img/partner-hover7.png",
    width: 95,
    height: 33,
  },
  {
    id: 10,
    defaultImage: "/images/partner-img/partner-4.png",
    hoverImage: "/images/partner-img/partner-hover4.png",
    width: 137,
    height: 25,
  },
  {
    id: 11,
    defaultImage: "/images/partner-img/partner-5.png",
    hoverImage: "/images/partner-img/partner-hover5.png",
    width: 89,
    height: 24,
  },
  {
    id: 12,
    defaultImage: "/images/partner-img/partner-3.png",
    hoverImage: "/images/partner-img/partner-hover3.png",
    width: 114,
    height: 22,
  },
  {
    id: 13,
    defaultImage: "/images/partner-img/partner-2.png",
    hoverImage: "/images/partner-img/partner-hover2.png",
    width: 100,
    height: 29,
  },
  {
    id: 14,
    defaultImage: "/images/partner-img/partner-4.png",
    hoverImage: "/images/partner-img/partner-hover4.png",
    width: 137,
    height: 25,
  },
  {
    id: 15,
    defaultImage: "/images/partner-img/partner-5.png",
    hoverImage: "/images/partner-img/partner-hover5.png",
    width: 89,
    height: 24,
  },
  {
    id: 16,
    defaultImage: "/images/partner-img/partner-7.png",
    hoverImage: "/images/partner-img/partner-hover7.png",
    width: 95,
    height: 33,
  },
  {
    id: 17,
    defaultImage: "/images/partner-img/partner-3.png",
    hoverImage: "/images/partner-img/partner-hover3.png",
    width: 114,
    height: 22,
  },
  {
    id: 18,
    defaultImage: "/images/partner-img/partner-6.png",
    hoverImage: "/images/partner-img/partner-hover6.png",
    width: 120,
    height: 33,
  },
];

const Partner = () => {
  return (
    <>
      <div className="ready-to-talk">
        <div className="container">
          <h3>Ready to talk?</h3>
          <p>Our team is here to answer your question about StartP</p>

          <Link href="/contact" className="btn btn-primary">
            Contact Us
          </Link>

          <span>
            <Link href="#">Or, get started now with a free trial</Link>
          </span>
        </div>
      </div>

      <div className="partner-area partner-section">
        <div className="container">
          <h5>More than 1.5 million businesses and organizations use StartP</h5>

          <div className="partner-inner">
            <div className="row justify-content-center">
              {partnerData.map((partner) => (
                <div
                  key={partner.id}
                  className="col-lg-2 col-md-3 col-6 partner-logo"
                >
                  <a href="#" target="_blank" rel="noopener noreferrer">
                    <Image
                      src={partner.defaultImage}
                      alt="partner"
                      width={partner.width}
                      height={partner.height}
                      className="default-image"
                    />
                    <Image
                      src={partner.hoverImage}
                      alt="partner hover"
                      width={partner.width}
                      height={partner.height}
                      className="hover-image"
                    />
                  </a>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Partner;
