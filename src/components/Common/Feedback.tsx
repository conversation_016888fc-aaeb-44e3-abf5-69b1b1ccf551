"use client";

import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import Image from "next/image";

const feedbackData = [
  {
    id: 1,
    name: "<PERSON>",
    title: "Web Developer",
    image: "/images/client-image/client1.jpg",
    feedback:
      "Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
  {
    id: 2,
    name: "<PERSON>",
    title: "Software Engineer",
    image: "/images/client-image/client2.jpg",
    feedback:
      "Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
  {
    id: 3,
    name: "<PERSON>",
    title: "Fictional Character",
    image: "/images/client-image/client3.jpg",
    feedback:
      "Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
  {
    id: 4,
    name: "Jason Momoa",
    title: "American Actor",
    image: "/images/client-image/client4.jpg",
    feedback:
      "Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
  {
    id: 5,
    name: "Gennady Korotkevich",
    title: "Competitive Programmer",
    image: "/images/client-image/client5.jpg",
    feedback:
      "Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
];

const Feedback: React.FC = () => {
  return (
    <div className="feedback-area ptb-80 bg-f7fafd">
      <div className="container">
        <div className="section-title">
          <h2>What users Saying</h2>
          <div className="bar"></div>
          <p>
            Hear directly from users who trust and love our services.<br />
            Real stories that reflect real satisfaction and success.
          </p>
        </div>

        <Swiper
          pagination={{
            clickable: true,
          }}
          autoplay={{
            delay: 6000,
            pauseOnMouseEnter: true,
          }}
          modules={[Autoplay, Pagination]}
          className="feedback-slides"
        >
          {feedbackData.map((feedback) => (
            <SwiperSlide key={feedback.id}>
              <div className="client-feedback">
                <div className="single-feedback">
                  <div className="client-img">
                    <Image
                      src={feedback.image}
                      alt={feedback.name}
                      width={95}
                      height={95}
                    />
                  </div>
                  <h3>{feedback.name}</h3>
                  <span>{feedback.title}</span>
                  <p>{feedback.feedback}</p>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* Shape Images */}
      <div className="shape1">
        <Image src="/images/shape1.png" alt="shape" width={202} height={202} />
      </div>
      <div className="shape2 rotateme">
        <Image src="/images/shape2.svg" alt="shape" width={22} height={22} />
      </div>
      <div className="shape3">
        <Image src="/images/shape3.svg" alt="shape" width={28} height={28} />
      </div>
      <div className="shape4">
        <Image src="/images/shape4.svg" alt="shape" width={21} height={20} />
      </div>
      <div className="shape5">
        <Image src="/images/shape5.png" alt="shape" width={182} height={146} />
      </div>
      <div className="shape6 rotateme">
        <Image src="/images/shape4.svg" alt="shape" width={21} height={20} />
      </div>
      <div className="shape7">
        <Image src="/images/shape4.svg" alt="shape" width={21} height={20} />
      </div>
      <div className="shape8 rotateme">
        <Image src="/images/shape2.svg" alt="shape" width={22} height={22} />
      </div>
    </div>
  );
};

export default Feedback;
