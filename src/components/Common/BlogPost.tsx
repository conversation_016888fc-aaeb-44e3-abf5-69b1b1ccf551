"use client";

import React, { useEffect, useState } from "react";
import { blogPostApi } from "@/api/blog/post";
import { IBlogPost, TBlogPost } from "@/types/blog";
import BlogPostBox from "@/components/Common/BlogPostBox";
import EffectWrapper from "@/components/EffectWrapper";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";
import { POST_LOADING_DATA } from "@/constants/app";
import { useTranslations } from 'next-intl';

const BlogPost = () => {
  const t = useTranslations('pages.home.blog');

  const [blogPosts, setBlogPosts] = useState<TBlogPost[]>([]);

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        const response = await blogPostApi.get<IBlogPost>({
          slug: 'custom-filters',
          params: {
            per_page: 10,
          },
        });
        setBlogPosts(response.data as TBlogPost[] || []);
      } catch (error) {
        console.error("Error fetching blog posts:", error);
      }
    };

    fetchBlogPosts();
  }, []);

  return (
    <div className="blog-area pt-80 pb-50">
      <div className="container">
        <div className="section-title">
          <h2>{t('title')}</h2>
          <div className="bar"></div>
          <p>{t('description')}</p>
        </div>

        <div className="">
          <EffectWrapper>
            <Swiper
              init={true}
              modules={[Autoplay, Pagination]}
              spaceBetween={32}
              loop={true}
              lazyPreloadPrevNext={5}
              mousewheel={true}
              scrollbar={false}
              speed={1500}
              autoplay={{
                delay: 2000,
                disableOnInteraction: false,
                pauseOnMouseEnter: false
              }}

              // stop if hovered
              onSwiper={(swiper) => {
                if (swiper.autoplay) {
                  swiper.autoplay.stop();
                  swiper.el.addEventListener('mouseenter', () => {
                    swiper.autoplay?.stop();
                  });
                  swiper.el.addEventListener('mouseleave', () => {
                    swiper.autoplay?.start();
                  });
                }
              }}

              pagination={false}
              breakpoints={{
                576: {
                  slidesPerView: 1
                },
                768: {
                  slidesPerView: 2
                },
                1366: {
                  slidesPerView: 3
                }
              }}
            >
              {blogPosts.length > 0 ?
                blogPosts.map((post: TBlogPost) => (
                  <SwiperSlide key={post.id}>
                    <BlogPostBox key={post.id} post={post} />
                  </SwiperSlide>
                )) :
                Array(4).fill(null).map((_, i) =>
                  <SwiperSlide key={i}>
                    <BlogPostBox
                      key={i}
                      post={POST_LOADING_DATA}
                    />
                  </SwiperSlide>)}
            </Swiper>
          </EffectWrapper>
        </div>
      </div>
    </div>
  );
};

export default BlogPost;
