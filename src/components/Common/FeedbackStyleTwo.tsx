"use client";

import React from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";

import shape1 from "/public/images/shape1.png";
import shape2 from "/public/images/shape2.svg";
import shape3 from "/public/images/shape3.svg";
import shape4 from "/public/images/shape4.svg";
import shape5 from "/public/images/shape5.png";

const feedbacks = [
  {
    id: 1,
    name: "<PERSON>",
    position: "Lead Developer at Envato",
    image: "/images/client-image/client1.jpg",
    feedback:
      "Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
  {
    id: 2,
    name: "<PERSON>",
    position: "Lead Developer at Envato",
    image: "/images/client-image/client2.jpg",
    feedback:
      "Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
  {
    id: 3,
    name: "Marta Smith",
    position: "Lead Developer at Envato",
    image: "/images/client-image/client3.jpg",
    feedback:
      "Quis ipsum suspendisse ultrices gravida. Risus commodo viverra maecenas accumsan lacus vel facilisis. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
];

const FeedbackStyleTwo = () => {
  return (
    <div className="feedback-area ptb-80">
      <div className="container">
        <div className="section-title">
          <h2>What Users Are Saying</h2>
          <div className="bar"></div>
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
        </div>

        <div className="testimonials-slides-box">
          <Swiper
            pagination={{
              clickable: true,
            }}
            autoplay={{
              delay: 6000,
              pauseOnMouseEnter: true,
            }}
            autoHeight={true}
            modules={[Pagination, Autoplay]}
            className="testimonials-slides"
          >
            {feedbacks.map((feedback) => (
              <SwiperSlide key={feedback.id}>
                <div className="single-feedback-item">
                  <div className="client-info align-items-center">
                    <div className="image">
                      <Image
                        src={feedback.image}
                        alt={feedback.name}
                        width={95}
                        height={95}
                      />
                    </div>

                    <div className="title">
                      <h3>{feedback.name}</h3>
                      <span>{feedback.position}</span>
                    </div>
                  </div>
                  <p>{feedback.feedback}</p>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>

      {/* Shape Images */}
      <div className="shape1">
        <Image src={shape1} alt="shape" width={202} height={202} />
      </div>
      <div className="shape2 rotateme">
        <Image src={shape2} alt="shape" width={22} height={22} />
      </div>
      <div className="shape3">
        <Image src={shape3} alt="shape" width={28} height={28} />
      </div>
      <div className="shape4">
        <Image src={shape4} alt="shape" width={21} height={20} />
      </div>
      <div className="shape5">
        <Image src={shape5} alt="shape" width={182} height={146} />
      </div>
      <div className="shape6 rotateme">
        <Image src={shape4} alt="shape" width={21} height={20} />
      </div>
      <div className="shape7">
        <Image src={shape4} alt="shape" width={21} height={20} />
      </div>
      <div className="shape8 rotateme">
        <Image src={shape2} alt="shape" width={22} height={22} />
      </div>
    </div>
  );
};

export default FeedbackStyleTwo;
