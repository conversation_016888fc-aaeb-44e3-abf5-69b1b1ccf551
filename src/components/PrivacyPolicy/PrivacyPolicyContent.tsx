import React from "react";
import Link from "next/link";
import { useTranslations } from 'next-intl';

const PrivacyPolicyContent = () => {
  const t = useTranslations('pages.privacy-policy');
  const tCommon = useTranslations('common');

  return (
    <>
      <div className="main-text-area ptb-80 landing-page-custom">
        <div className="container">
          <h2>{t('privacy-policy1.title')}</h2>
          <p>{t('privacy-policy1.content')}<strong>
            <Link
              href={'/'}
              className="text-base font-medium text-primary"
              title="cslant.com"
              aria-label="cslant.com"
            >
              cslant.com
            </Link>
          </strong>{t('privacy-policy1.content1')}<strong>
            <Link
              href={'/'}
              className="text-base font-medium text-primary"
              title="cslant.com"
              aria-label="cslant.com"
            >
              cslant.com
            </Link>
          </strong>.
          </p>
          <p>{t('privacy-policy1.content2')}</p>

          <h2>{t('privacy-policy2.title')}</h2>
          <p>{t.rich('privacy-policy2.content', {
              bold: (chunks) => <strong>{chunks}</strong>
          })}</p>
          <p>{t('privacy-policy2.content1')}</p>

          <h2>{t('privacy-policy3.title')}</h2>
          <p>{t('privacy-policy3.content')}</p>
          <ul>
            <li>{t('privacy-policy3.subpoints.subpoint1')}</li>
            <li>{t('privacy-policy3.subpoints.subpoint2')}</li>
            <li>{t('privacy-policy3.subpoints.subpoint3')}</li>
            <li>{t('privacy-policy3.subpoints.subpoint4')}</li>
            <li>{t('privacy-policy3.subpoints.subpoint5')}</li>
            <li>{t('privacy-policy3.subpoints.subpoint6')}</li>
            <li>{t('privacy-policy3.subpoints.subpoint7')}</li>
            <li>{t('privacy-policy3.subpoints.subpoint8')}</li>
          </ul>
          <p>{t.rich('privacy-policy3.content2', {
            bold: (chunks) => <strong>{chunks}</strong>
          })}</p>

          <h2>{t('privacy-policy4.title')}</h2>
          <p>{t.rich('privacy-policy4.content', {
            bold: (chunks) => <strong>{chunks}</strong>
          })}</p>
          <p>{t.rich('privacy-policy4.content1', {
            bold: (chunks) => <strong>{chunks}</strong>
          })}</p>
          <ul>
            <h3>{t('privacy-policy4.subcontent1.title')}</h3>
            <p className="text-base !leading-relaxed text-body-color dark:text-body-color-dark">{t('privacy-policy4.subcontent1.content')}<strong>
              <Link
                href={'/'}
                className="text-base font-medium text-primary"
                title="cslant.com"
                aria-label="cslant.com"
              >
                cslant.com
              </Link>
            </strong>{t('privacy-policy4.subcontent1.content1')}{' '}
              <Link
                href={'/cookie-policy'}
                className="text-base font-medium text-primary"
                title="CSlant Cookie Policy"
                aria-label="CSlant Cookie Policy"
              >{tCommon('cookie-policy')}</Link>.
            </p>
          </ul>

          <h2>{t('privacy-policy5.title')}</h2>
          <p>{t.rich('privacy-policy5.content', {
            bold: (chunks) => <strong>{chunks}</strong>
          })}</p>

          <h2>{t('privacy-policy6.title')}</h2>
          <p>{t.rich('privacy-policy6.content', {
            bold: (chunks) => <strong>{chunks}</strong>
          })}</p>

          <h2>{t('privacy-policy7.title')}</h2>
          <p>{t.rich('privacy-policy7.content', {
            bold: (chunks) => <strong>{chunks}</strong>
          })}</p>

          <h2>{t('privacy-policy8.title')}</h2>
          <p>{t('privacy-policy8.content')}</p>

          <h2>{t('privacy-policy9.title')}</h2>
          <p>{t.rich('privacy-policy9.content', {
            bold: (chunks) => <strong>{chunks}</strong>
          })}</p>

          <h2>{t('privacy-policy10.title')}</h2>
          <p>{t.rich('privacy-policy10.content', {
            bold: (chunks) => <strong>{chunks}</strong>
          })}</p>

          <h2>{t('privacy-policy11.title')}</h2>
          <p>{t.rich('privacy-policy11.content', {
            bold: (chunks) => <strong>{chunks}</strong>
          })}</p>

          <h2>{t('privacy-policy12.title')}</h2>
          <ul>
            <h3>{t('privacy-policy12.subcontent1.title')}</h3>
            <p>{t('privacy-policy12.subcontent1.content')}</p>

            <h3>{t('privacy-policy12.subcontent2.title')}</h3>
            <p>{t('privacy-policy12.subcontent2.content')}</p>

            <p>{t('privacy-policy12.subcontent2.content1')}</p>
          </ul>
        </div>
      </div>
    </>
  );
};

export default PrivacyPolicyContent;
