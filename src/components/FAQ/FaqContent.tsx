'use client';

import React from 'react';
import {
  Accordion,
  AccordionItem,
  AccordionItemButton,
  AccordionItemHeading,
  AccordionItemPanel
} from 'react-accessible-accordion';
import { faqData } from '@/api/faq';
import ContactForm from '@/components/FAQ/ContactForm';
import { useTranslations } from 'next-intl';
import { formatTitleNormalized } from '@/utils/helper';

const FaqContent = () => {
  const t = useTranslations('pages.faq');

  return (
    <div className="faq-area ptb-80">
      <div className="container">
        {faqData.map((category) => {
          const titleKey= formatTitleNormalized(category.title);
          return <div key={category.id} className="faq-category">
            <div className="faq-accordion mb-4">
              <Accordion allowZeroExpanded>
                <h3 className="faq-category-title mb-3">{t(`${titleKey}.title`)}</h3>
                {category.items.map((item,idx) => {
                  const count = idx + 1;
                  return <AccordionItem key={item.id} uuid={item.id}>
                    <AccordionItemHeading>
                      <AccordionItemButton>
                        <span>{t(`${titleKey}.question${count}`)}</span>
                      </AccordionItemButton>
                    </AccordionItemHeading>
                    <AccordionItemPanel>
                      <p>{t(`${titleKey}.answer${count}`)}</p>
                    </AccordionItemPanel>
                  </AccordionItem>;
                })}
              </Accordion>
            </div>
          </div>;
        })}
        <hr />
        <ContactForm />
      </div>
    </div>
  );
};

export default FaqContent;
