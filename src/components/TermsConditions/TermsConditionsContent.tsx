import React from "react";
import Link from "next/link";
import { CSLANT_CONTACT_EMAIL } from "@/constants/app";
import { useTranslations } from 'next-intl';

const TermsAndConditionsPage: React.FC = () => {
  const t = useTranslations('pages.terms-conditions');
  const tCommon = useTranslations('common');

  return (
    <>
      <div id="terms-and-conditions" className="main-text-area ptb-80 landing-page-custom">
        <div className="container">
          <h1>{t('terms-and-conditions-for-cslant')}</h1>
          <p>
            <strong>{t('effective-date')}</strong> 12/28/2024
          </p>

          <p>{t('terms-and-conditions-for-cslant-description')}</p>

          <h2>{t('terms-and-conditions1.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions1.content')}</p>

          <h2>{t('terms-and-conditions2.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions2.content')}</p>

          <h2>{t('terms-and-conditions3.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions3.content')}</p>
          <ul>
            <li>{t('terms-and-conditions3.subpoints.subpoint1')}</li>
            <li>{t('terms-and-conditions3.subpoints.subpoint2')}</li>
            <li>{t('terms-and-conditions3.subpoints.subpoint3')}</li>
          </ul>

          <h2>{t('terms-and-conditions4.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions4.content')}</p>

          <h2>{t('terms-and-conditions5.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions5.content')}</p>
          <ul>
            <li>{t('terms-and-conditions5.subpoints.subpoint1')}</li>
            <li>{t('terms-and-conditions5.subpoints.subpoint2')}</li>
          </ul>

          <h2>{t('terms-and-conditions6.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions6.content')}</p>

          <h2>{t('terms-and-conditions7.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions7.content')}</p>

          <h2>{t('terms-and-conditions8.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions8.content')}</p>

          <h2>{t('terms-and-conditions9.title')}</h2>
          <p className="pre-line">
            {t('terms-and-conditions9.content1')}
            <Link href="/privacy-policy">
              <strong>{tCommon('cslant-privacy-policy')}</strong>
            </Link>{t('terms-and-conditions9.content2')}
          </p>

          <h2>{t('terms-and-conditions10.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions10.content')}</p>

          <h2>{t('terms-and-conditions11.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions11.content')}</p>

          <h2>{t('terms-and-conditions12.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions12.content')}</p>

          <h2>{t('terms-and-conditions13.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions13.content')}</p>

          <h2>{t('terms-and-conditions14.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions14.content')}</p>

          <h2>{t('terms-and-conditions15.title')}</h2>
          <p className="pre-line">{t('terms-and-conditions15.content')}</p>
          <p>
            <strong>Email:</strong>{' '}
            <a href={`mailto:${CSLANT_CONTACT_EMAIL}`} className="text-primary">
              {CSLANT_CONTACT_EMAIL}
            </a>
          </p>
        </div>
      </div>
    </>
  );
};

export default TermsAndConditionsPage;
