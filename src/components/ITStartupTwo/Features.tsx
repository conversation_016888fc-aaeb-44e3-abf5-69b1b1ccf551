"use client";

import React from "react";
import * as Icon from "react-feather";
import Link from "next/link";

const featuresData = [
  {
    id: 1,
    title: "Zero Configuration",
    description:
      "Lorem ipsum dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna.",
    icon: <Icon.Server />,
    link: "/features/feature-details/",
    delay: 100,
    background: "",
  },
  {
    id: 2,
    title: "Code Security",
    description:
      "Lorem ipsum dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna.",
    icon: <Icon.Code />,
    link: "/features/feature-details/",
    delay: 200,
    background: "bg-f78acb",
  },
  {
    id: 3,
    title: "Team Management",
    description:
      "Lorem ipsum dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna.",
    icon: <Icon.Users />,
    link: "/features/feature-details/",
    delay: 300,
    background: "bg-c679e3",
  },
  {
    id: 4,
    title: "Access Controlled",
    description:
      "Lorem ipsum dolor sit amet elit, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna.",
    icon: <Icon.GitBranch />,
    link: "/features/feature-details/",
    delay: 400,
    background: "bg-eb6b3d",
  },
];

const Features: React.FC = () => {
  return (
    <>
      <div className="boxes-area">
        <div className="container">
          <div className="row justify-content-center">
            {featuresData.slice(0, 4).map((feature) => (
              <div
                key={feature.id}
                className={`col-lg-3 col-md-6 ${feature.background}`}
                data-aos="fade-up"
                data-aos-delay={feature.delay}
                data-aos-duration="700"
                data-aos-once="true"
              >
                <div className={`single-box ${feature.background}`}>
                  <div className="icon">{feature.icon}</div>

                  <h3>
                    <Link href={feature.link}>{feature.title}</Link>
                  </h3>

                  <p>{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default Features;
