"use client";

import React from "react";
import Link from "next/link";
import * as Icon from "react-feather";

// Services Data
const servicesData = [
  {
    id: 1,
    icon: <Icon.Settings />,
    iconBg: "",
    title: "Incredible Infrastructure",
    description:
      "Lorem ipsum dolor amet, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 100,
  },
  {
    id: 2,
    icon: <Icon.Mail />,
    iconBg: "",
    title: "Email Notifications",
    description:
      "Lorem ipsum dolor amet, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 200,
  },
  {
    id: 3,
    icon: <Icon.Bell />,
    iconBg: "",
    title: "Best Analytics Audits",
    description:
      "Lorem ipsum dolor amet, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 300,
  },
  {
    id: 4,
    icon: <Icon.Grid />,
    iconBg: "bg-c679e3",
    title: "Simple Dashboard",
    description:
      "Lorem ipsum dolor amet, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 400,
  },
  {
    id: 5,
    icon: <Icon.Info />,
    iconBg: "bg-c679e3",
    title: "Information Retrieval",
    description:
      "Lorem ipsum dolor amet, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 500,
  },
  {
    id: 6,
    icon: <Icon.HardDrive />,
    iconBg: "bg-c679e3",
    title: "Deep Technical SEO",
    description:
      "Lorem ipsum dolor amet, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 600,
  },
  {
    id: 7,
    icon: <Icon.MousePointer />,
    iconBg: "bg-eb6b3d",
    title: "Drag & Drop Functionality",
    description:
      "Lorem ipsum dolor amet, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 700,
  },
  {
    id: 8,
    icon: <Icon.Bell />,
    iconBg: "bg-eb6b3d",
    title: "Deadline Reminders",
    description:
      "Lorem ipsum dolor amet, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 800,
  },
  {
    id: 9,
    icon: <Icon.Send />,
    iconBg: "bg-eb6b3d",
    title: "Modern Keyword Analysis",
    description:
      "Lorem ipsum dolor amet, adipiscing, sed do eiusmod tempor incididunt ut labore dolore magna aliqua.",
    link: "/services/service-details/",
    delay: 900,
  },
];

const OurServices: React.FC = () => {
  return (
    <>
      <div className="services-area-two pt-80 pb-50 bg-f7fafd">
        <div className="container">
          <div className="section-title">
            <h2>Our Services</h2>
            <div className="bar"></div>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
          </div>

          <div className="row justify-content-center">
            {servicesData.map((service) => (
              <div
                key={service.id}
                className="col-lg-4 col-md-6 col-sm-6"
                data-aos="fade-in"
                data-aos-delay={service.delay}
                data-aos-duration="700"
                data-aos-once="true"
              >
                <div className="single-services-box">
                  <div className={`icon ${service.iconBg}`}>{service.icon}</div>
                  <h3>
                    <Link href={service.link}>{service.title}</Link>
                  </h3>
                  <p>{service.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default OurServices;
