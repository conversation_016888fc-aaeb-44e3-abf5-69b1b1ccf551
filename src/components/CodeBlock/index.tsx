'use client'

import { CopyBlock, dracula } from 'react-code-blocks'
import React from 'react'

interface CodeBlockProps {
  code: string
  language: string
}

const CodeBlock: React.FC<CodeBlockProps> = ({ code, language }) => {
  return (
    <div style={{ margin: '20px 0' }}>
      <CopyBlock
        text={code}
        language={language}
        showLineNumbers={true}
        theme={dracula}
        customStyle={{
          padding: '20px',
          borderRadius: '8px',
          fontSize: '14px',
          fontFamily: 'SF Mono, Monaco, Inconsolata, Roboto Mono, Source Code Pro, monospace',
        }}
        wrapLongLines={true}
      />
    </div>
  )
}

export default CodeBlock
