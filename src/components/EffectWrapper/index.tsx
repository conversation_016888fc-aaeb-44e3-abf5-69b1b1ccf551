import React from 'react';
import { motion } from 'framer-motion';
import { IEffectWrapper } from '@/types/effect';

const EffectWrapper: React.FC<IEffectWrapper> = ({ ...props }: IEffectWrapper) => {
  const { children, variants, effectTime = 0.8, delayTime = 0.1 } = props;
  return <motion.div
    variants={variants ?? {
      hidden: {
        opacity: 0,
        y: -20
      },
      visible: {
        opacity: 1,
        y: 0
      }
    }}
    initial="hidden"
    whileInView="visible"
    transition={{ duration: effectTime, delay: delayTime }}
    viewport={{ once: true }}

  >
    {children}
  </motion.div>;
};

export default EffectWrapper;
