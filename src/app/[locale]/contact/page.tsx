import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import ContactInfo from "@/components/Contact/ContactInfo";
import GoogleMap from "@/components/Contact/GoogleMap";
import ContactForm from "@/components/Contact/ContactForm";
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';
import { generatePageMetadata } from "@/utils/metadata";

export async function generateMetadata() {
  const locale = await getLocale();
  const t = await getTranslations('pages.contact');
  
  return generatePageMetadata({
    title: t('title', { default: 'Contact' }),
    description: t('description', { default: 'Contact CSlant for inquiries, support, or partnership opportunities.' }),
    path: 'contact',
    locale,
    imageUrl: 'https://cslant.com/images/og-contact.jpg',
    type: 'website'
  });
}

export default function Page() {
  const t = useTranslations('pages.contact');

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={t('title')} />

      <ContactInfo />

      <GoogleMap />

      <ContactForm />

      <Footer />
    </>
  );
};
