import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import TermsConditionsContent from "@/components/TermsConditions/TermsConditionsContent";
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';
import { generatePageMetadata } from "@/utils/metadata";

export async function generateMetadata() {
  const locale = await getLocale();
  const t = await getTranslations('pages.terms-conditions');
  
  return generatePageMetadata({
    title: t('title', { default: 'Terms & Conditions' }),
    description: t('description', { default: 'Read CSlant\'s terms and conditions for using our services and website.' }),
    path: 'terms-conditions',
    locale,
    type: 'website',
    noindex: true  // Terms and conditions pages often don't need to be indexed
  });
}

export default function Page() {
  const t = useTranslations('pages.terms-conditions');
  
  return (
    <>
      <Navbar />

      <PageBanner 
        pageTitle={t('title')} 
        headingNumber={1}
      />

      <TermsConditionsContent />

      <Footer />
    </>
  );
};
