import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import PrivacyPolicyContent from "@/components/PrivacyPolicy/PrivacyPolicyContent";
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';
import { generatePageMetadata } from "@/utils/metadata";

export async function generateMetadata() {
  const locale = await getLocale();
  const t = await getTranslations('pages.privacy-policy');
  
  return generatePageMetadata({
    title: t('title', { default: 'Privacy Policy' }),
    description: t('description', { default: 'Learn about CSlant\'s privacy policy and how we protect your personal information.' }),
    path: 'privacy-policy',
    locale,
    type: 'website',
    noindex: true  // Privacy policy pages often don't need to be indexed
  });
}

export default function Page() {
  const t = useTranslations('pages.privacy-policy');
  
  return (
    <>
      <Navbar />

      <PageBanner 
        pageTitle={t('title')} 
        headingNumber={1} 
      />

      <PrivacyPolicyContent />

      <Footer />
    </>
  );
};
