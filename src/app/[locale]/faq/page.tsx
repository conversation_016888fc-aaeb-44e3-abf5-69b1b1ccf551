import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import FaqContent from "@/components/FAQ/FaqContent";
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';
import { generatePageMetadata } from "@/utils/metadata";

export async function generateMetadata() {
  const locale = await getLocale();
  const t = await getTranslations('pages.faq');
  
  return generatePageMetadata({
    title: t('title', { default: 'FAQ' }),
    description: t('description', { default: 'Find answers to frequently asked questions about CSlant services and solutions.' }),
    path: 'faq',
    locale,
    imageUrl: 'https://cslant.com/images/og-faq.jpg',
    type: 'website'
  });
}

export default function Page() {
  const t = useTranslations('pages.faq');

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={t('title')} />

      <FaqContent />

      <Footer />
    </>
  );
};
