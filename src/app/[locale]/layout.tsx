import React from "react";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { locales } from '@/i18n';
import { assets } from "@/utils/helper";
import { notFound } from "next/navigation";
import { generatePageMetadata } from "@/utils/metadata";

type Props = {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
};

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export async function generateMetadata({ params }: Props) {
  const { locale } = await params;
  const messages = await getMessages();
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://cslant.com';
  const defaultImage = assets('images/head/og-image.jpg');

  // Generate metadata using our helper
  return generatePageMetadata({
    title: (messages as any).home?.title || "Home - CSlant Solutions | Technology Consulting",
    description: (messages as any).home?.description ||
      "CSlant is a leading technology solutions provider offering software development, IT consulting, and digital transformation services to help businesses thrive in the digital age.",
    path: '', // Empty for home page
    locale,
    type: 'website',
    metadataBase: new URL(baseUrl),
    imageUrl: defaultImage,
    // Add template for title
    titleTemplate: '%s | CSlant Solutions',
    // Additional meta tags
    authors: ['CSlant Team'],
    tags: ['technology', 'consulting', 'software development', 'digital transformation'],
    // OpenGraph and Twitter will be handled by generatePageMetadata
  });
}

export default async function LocaleLayout({
  children,
  params
}: Props) {
  const { locale } = await params;

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) {
    notFound();
  }

  // Providing all messages to the client side
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      {children}
    </NextIntlClientProvider>
  );
}
