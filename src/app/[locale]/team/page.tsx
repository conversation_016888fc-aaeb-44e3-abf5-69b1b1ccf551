import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import TeamMembers from "@/components/Team/TeamMembers";
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';
import { generatePageMetadata } from "@/utils/metadata";

export async function generateMetadata() {
  const locale = await getLocale();
  const t = await getTranslations('pages.team');
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://cslant.com';
  const title = t('title', { default: 'Our Team' });
  const description = t('description', { default: 'Meet the talented team behind CSlant.' });
  
  return generatePageMetadata({
    title,
    description,
    path: 'team',
    locale,
    type: 'website',
    imageUrl: `${baseUrl}/images/og-team.jpg`,
    authors: ['CSlant Team'],
    tags: ['team', 'about us', 'company']
  });
}

export default function Page() {
  const t = useTranslations('pages.team');

  return (
    <>
      <Navbar />

      <PageBanner
        pageTitle={t('title')}
        headingNumber={1}
      />

      <TeamMembers />

      <Footer />
    </>
  );
};
