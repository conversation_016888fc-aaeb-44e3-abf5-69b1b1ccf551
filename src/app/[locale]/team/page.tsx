import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import TeamMembers from "@/components/Team/TeamMembers";
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const t = await getTranslations('pages.team');
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}team`;
  const title = t('title', { default: 'Team | CSlant' });
  const description = t('description', { default: 'Meet the talented team behind CSlant.' });
  const imageUrl = 'https://cslant.com/images/og-team.jpg';

  return {
    title: {
      default: title,
      template: '%s | CSlant',
    },
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CS<PERSON>', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: 'https://cslant.com/team',
      languages: {
        en: 'https://cslant.com/team',
        vi: 'https://cslant.com/vi/team',
      },
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      // images: [
      //   {
      //     url: imageUrl,
      //     width: 1200,
      //     height: 630,
      //     alt: title,
      //   },
      // ],
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
      // images: [imageUrl],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

  };
}

export default function Page() {
  const t = useTranslations('pages.team');

  return (
    <>
      <Navbar />

      <PageBanner
        pageTitle={t('title')}
        headingNumber={1}
      />

      <TeamMembers />

      <Footer />
    </>
  );
};
