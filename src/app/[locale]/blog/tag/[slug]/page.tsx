import React from "react";
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import TagBlogGrid from "@/components/Tag/TagBlogGrid";
import { Metadata } from 'next';
import { getLocale, getTranslations } from 'next-intl/server';
import { generateMetadataTitle } from "@/utils/seo";
import { tagApi } from '@/api/blog/tag';
import { blogPostApi } from '@/api/blog/post';
import { notFound } from 'next/navigation';
import { PAGE_SIZE_12 } from '@/types/page';

interface TagPageProps {
  params: Promise<{
    slug: string;
    locale: string;
  }>;
  searchParams: Promise<{
    page?: string;
    per_page?: string;
  }>;
}

async function fetchTagBySlug(slug: string) {
  try {
    const response = await tagApi.get({
      slug: slug
    });
    return response;
  } catch (error) {
    console.error('Error fetching tag:', error);
    throw error;
  }
}

async function fetchBlogsByTag(tagSlug: string, page: number, perPage: number) {
  try {
    const response = await blogPostApi.get({
      params: {
        tag_slug: tagSlug,
        page,
        per_page: perPage
      }
    });
    return response;
  } catch (error) {
    console.error('Error fetching blogs by tag:', error);
    throw error;
  }
}

export async function generateMetadata({ params }: TagPageProps): Promise<Metadata> {
  const { slug } = await params;
  const locale = await getLocale();
  const t = await getTranslations('pages.tags');

  // Fetch tag info for metadata
  let tagName = slug;
  try {
    const tagResponse = await fetchTagBySlug(slug);
    if (tagResponse.data?.name) {
      tagName = tagResponse.data.name;
    }
  } catch (error) {
    // Use slug as fallback
  }

  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}blog/tag/${slug}`;
  const title = `${tagName} | ${t('title', { default: 'Tags' })}`;
  const description = `Browse all blog posts tagged with ${tagName}.`;
  const imageUrl = 'https://cslant.com/images/og-tags.jpg';

  const titleConfig = {
    title: title,
    template: '%s | CSlant'
  };

  return {
    ...generateMetadataTitle(titleConfig),
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: `https://cslant.com/blog/tag/${slug}`,
      languages: {
        en: `https://cslant.com/blog/tag/${slug}`,
        vi: `https://cslant.com/vi/blog/tag/${slug}`,
      },
    },
    other: {
      'lang': locale
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
      images: [imageUrl],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function TagPage({ params, searchParams }: TagPageProps) {
  const { slug } = await params;
  const searchParamsData = await searchParams;
  const page = Number(searchParamsData.page) || 1;
  const perPage = PAGE_SIZE_12;

  let tagResponse;
  let blogsResponse;

  try {
    // Fetch tag info
    tagResponse = await fetchTagBySlug(slug);
    if (!tagResponse.data || tagResponse.error) {
      notFound();
    }

    // Fetch blogs by tag
    blogsResponse = await fetchBlogsByTag(slug, page, perPage);
    if (!blogsResponse.data || blogsResponse.error) {
      notFound();
    }
  } catch (error) {
    notFound();
  }

  const tagName = tagResponse.data.name || slug;

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={tagName} />

      <TagBlogGrid
        blogs={blogsResponse.data}
        meta={blogsResponse.meta}
        tagName={tagName}
      />

      <Footer />
    </>
  );
};
