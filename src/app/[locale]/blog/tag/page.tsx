import React from 'react';
import Navbar from '@/components/Layout/Navbar';
import Footer from '@/components/Layout/Footer';
import PageBanner from '@/components/Common/PageBanner';
import TagGrid from '@/components/Tag/TagGrid';
import Pagination from '@/components/Common/Pagination';
import { Metadata } from 'next';
import { getLocale, getTranslations } from 'next-intl/server';
import { generateMetadataTitle } from '@/utils/seo';
import { tagApi } from '@/api/blog/tag';
import { notFound } from 'next/navigation';
import { PAGE_SIZE_12 } from '@/types/page';

interface TagsPageProps {
  searchParams: Promise<{
    page?: string;
    per_page?: string;
  }>;
}

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const t = await getTranslations('pages.tags');
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}tags`;
  const title = t('title', { default: 'Tags | CSlant' });
  const description = t('description', { default: 'Browse all tags and topics on CSlant.' });
  const imageUrl = 'https://cslant.com/images/og-tags.jpg';

  const titleConfig = {
    title: title,
    template: '%s | CSlant'
  };

  return {
    ...generateMetadataTitle(titleConfig),
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: url,
      languages: {
        en: 'https://cslant.com/blog/tag',
        vi: 'https://cslant.com/vi/blog/tag',
      },
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
      images: [imageUrl],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

async function fetchTags(page: number, perPage: number) {
  try {
    return await tagApi.get({
      params: {
        page,
        per_page: perPage
      }
    });
  } catch (error) {
    console.error('Error fetching tags:', error);
    throw error;
  }
}

export default async function TagsPage({ searchParams }: TagsPageProps) {
  const params = await searchParams;
  const page = Number(params.page) || 1;
  const perPage = PAGE_SIZE_12;

  const t = await getTranslations('pages.tags');

  let tagsResponse;

  try {
    tagsResponse = await fetchTags(page, perPage);
  } catch (error) {
    notFound();
  }

  if (!tagsResponse.data || tagsResponse.error) {
    notFound();
  }

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={t('title', { default: 'Tags' })} />

      <div className="blog-area ptb-80">
        <div className="container">
          <div className="row justify-content-between">
            <TagGrid tags={tagsResponse.data} />

            {tagsResponse.meta && tagsResponse.meta.last_page > 1 && (
              <Pagination
                currentPage={tagsResponse.meta.current_page}
                lastPage={tagsResponse.meta.last_page}
                total={tagsResponse.meta.total}
                perPage={PAGE_SIZE_12}
                from={tagsResponse.meta.from}
                to={tagsResponse.meta.to}
              />
            )}
          </div>
        </div>
      </div>

      <Footer />
    </>
  );
};
