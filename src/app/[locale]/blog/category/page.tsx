import React from 'react';
import Navbar from '@/components/Layout/Navbar';
import Footer from '@/components/Layout/Footer';
import PageBanner from '@/components/Common/PageBanner';
import CategoryGrid from '@/components/Category/CategoryGrid';
import Pagination from '@/components/Common/Pagination';
import { Metadata } from 'next';
import { getLocale, getTranslations } from 'next-intl/server';
import { generateMetadataTitle } from '@/utils/seo';
import { categoryApi } from '@/api/blog/category';
import { notFound } from 'next/navigation';
import { PAGE_SIZE_12 } from '@/types/page';

interface CategoriesPageProps {
  searchParams: Promise<{
    page?: string;
    per_page?: string;
  }>;
}

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const t = await getTranslations('pages.categories');
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}blog/category`;
  const title = t('title', { default: 'Categories | CSlant' });
  const description = t('description', { default: 'Browse all categories and topics on CSlant.' });
  const imageUrl = 'https://cslant.com/images/og-categories.jpg';

  const titleConfig = {
    title: title,
    template: '%s | CSlant'
  };

  return {
    ...generateMetadataTitle(titleConfig),
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: 'https://cslant.com/blog/category',
      languages: {
        en: 'https://cslant.com/blog/category',
        vi: 'https://cslant.com/vi/blog/category'
      }
    },
    other: {
      'lang': locale
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website'
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
      images: [imageUrl],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1
      }
    }
  };
}

async function fetchCategories(page: number = 1, perPage: number = 12) {
  try {
    return await categoryApi.get({
      params: {
        page,
        per_page: perPage
      }
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }
}

export default async function CategoriesPage({ searchParams }: CategoriesPageProps) {
  const params = await searchParams;
  const page = Number(params.page) || 1;
  const perPage = PAGE_SIZE_12

  const t = await getTranslations('pages.categories');

  let categoriesResponse;

  try {
    categoriesResponse = await fetchCategories(page, perPage);
  } catch (error) {
    notFound();
  }

  if (!categoriesResponse.data || categoriesResponse.error) {
    notFound();
  }

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={t('title', { default: 'Categories' })} />

      <div className="blog-area ptb-80">
        <div className="container">
          <div className="row justify-content-between">
            <CategoryGrid categories={categoriesResponse.data} />

            {categoriesResponse.meta && categoriesResponse.meta.last_page > 1 && (
              <Pagination
                currentPage={categoriesResponse.meta.current_page}
                lastPage={categoriesResponse.meta.last_page}
                total={categoriesResponse.meta.total}
                perPage={PAGE_SIZE_12}
                from={categoriesResponse.meta.from}
                to={categoriesResponse.meta.to}
              />
            )}
          </div>
        </div>
      </div>

      <Footer />
    </>
  );
};
