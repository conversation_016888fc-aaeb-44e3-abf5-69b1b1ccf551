import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import BlogGridPost from "@/components/Blog/BlogGridPost";
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';
import { generatePageMetadata } from "@/utils/metadata";

export async function generateMetadata() {
  const locale = await getLocale();
  const t = await getTranslations('pages.blog');
  
  return generatePageMetadata({
    title: t('title', { default: 'Blog' }),
    description: t('description', { default: 'Read the latest news, insights, and updates from CSlant.' }),
    path: 'blog',
    locale,
    imageUrl: 'https://cslant.com/images/og-blog.jpg',
    type: 'website'
  });
}

export default function Page() {
  const t = useTranslations('pages.blog');

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={t('title')} />

      <BlogGridPost />

      <Footer />
    </>
  );
};
