import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import CookiePolicyContent from "@/components/CookiePolicy/CookiePolicyContent";
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const t = await getTranslations('pages.cookie-policy');
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}cookie-policy`;
  const title = t('title', { default: 'Cookie Policy | CSlant' });
  const description = t('description', { default: 'Learn about CSlant\'s cookie policy and how we use cookies on our website.' });
  // const imageUrl = 'https://cslant.com/images/og-cookie-policy.jpg';

  return {
    title: {
      default: title,
      template: '%s | CSlant',
    },
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: 'https://cslant.com/cookie-policy',
      languages: {
        en: 'https://cslant.com/cookie-policy',
        vi: 'https://cslant.com/vi/cookie-policy',
      },
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

  };
}

export default function Page() {
  const t = useTranslations('pages.cookie-policy');
  
  return (
    <>
      <Navbar />

      <PageBanner 
        pageTitle={t('title')} 
        headingNumber={1} 
      />

      <CookiePolicyContent />

      <Footer />
    </>
  );
};
