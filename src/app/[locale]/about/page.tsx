import React from "react";
import { getTranslations } from 'next-intl/server';
import { Metadata } from 'next';
import { getLocale } from 'next-intl/server';
import Navbar from "@/components/Layout/Navbar";
import Team from "@/components/Common/Team";
import FunFactsArea from "@/components/Common/FunFactsArea";
import Partner from "@/components/Common/Partner";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import AboutUsContent1 from "@/components/AboutUs/AboutUsContent1";
import AboutUsV1Content1 from "@/components/AboutUs/AboutUsV1Content1";
import AboutUsV1Content2 from "@/components/AboutUs/AboutUsV1Content2";
import Technologies from "@/components/Common/Technologies";
import { useTranslations } from "next-intl";
import { generateMetadataTitle } from "@/utils/seo";

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const t = await getTranslations('pages.about');
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}about`;
  const title = t('title', { default: 'About Us | CSlant' });
  const description = t('description', { default: 'Learn more about CSlant, our team, mission, and technologies.' });
  const imageUrl = 'https://cslant.com/images/og-about.jpg';

  const titleConfig = {
    title: title,
    template: '%s | CSlant'
  };

  return {
    ...generateMetadataTitle(titleConfig),
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: 'https://cslant.com/about',
      languages: {
        'en': 'https://cslant.com/about',
        'vi': 'https://cslant.com/vi/about',
      },
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      // images: [
      //   {
      //     url: imageUrl,
      //     width: 1200,
      //     height: 630,
      //     alt: title,
      //   },
      // ],
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
      // images: [imageUrl],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

  };
}

export default function Page() {
  const t = useTranslations('pages.about');

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={t('title')} headingNumber={1} />

      <AboutUsContent1 />

      <AboutUsV1Content1 />

      <AboutUsV1Content2 />

      <Team />

      {/*<Partner />*/}
      <Technologies />

      <FunFactsArea />

      <Footer />
    </>
  );
}
