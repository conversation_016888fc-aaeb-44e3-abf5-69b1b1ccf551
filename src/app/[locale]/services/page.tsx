import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import ServicesStyle1 from "@/components/Services/ServicesStyle1";
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const t = await getTranslations('pages.services');
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}services`;
  const title = t('title', { default: 'Services | CSlant' });
  const description = t('description', { default: 'Discover our comprehensive range of software development and technology services.' });
  // const imageUrl = 'https://cslant.com/images/og-services.jpg';

  return {
    title: {
      default: title,
      template: '%s | CSlant',
    },
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: 'https://cslant.com/services',
      languages: {
        en: 'https://cslant.com/services',
        vi: 'https://cslant.com/vi/services',
      },
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

  };
}

export default function Page() {
  const t = useTranslations('pages.services');
  
  return (
    <>
      <Navbar />

      <PageBanner 
        pageTitle={t('title')} 
        headingNumber={1}
      />

      <ServicesStyle1 />

      <Footer />
    </>
  );
};

