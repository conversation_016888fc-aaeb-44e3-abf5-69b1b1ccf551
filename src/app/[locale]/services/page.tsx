import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import ServicesStyle1 from "@/components/Services/ServicesStyle1";
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';
import { generatePageMetadata } from "@/utils/metadata";

export async function generateMetadata() {
  const locale = await getLocale();
  const t = await getTranslations('pages.services');
  
  return generatePageMetadata({
    title: t('title', { default: 'Services' }),
    description: t('description', { default: 'Discover our comprehensive range of software development and technology services.' }),
    path: 'services',
    locale,
    imageUrl: 'https://cslant.com/images/og-services.jpg',
    type: 'website'
  });
}

export default function Page() {
  const t = useTranslations('pages.services');
  
  return (
    <>
      <Navbar />

      <PageBanner 
        pageTitle={t('title')} 
        headingNumber={1}
      />

      <ServicesStyle1 />

      <Footer />
    </>
  );
};

