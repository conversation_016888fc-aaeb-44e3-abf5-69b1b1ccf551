import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "@/components/Layout/Navbar";
import Footer from "@/components/Layout/Footer";
import PageBanner from "@/components/Common/PageBanner";
import ServiceDetailsContent from "@/components/Services/ServiceDetailsContent";
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { getLocale } from 'next-intl/server';

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale();
  const t = await getTranslations('pages.service-details');
  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}services/service-details`;
  const title = t('title', { default: 'Service Details | CSlant' });
  const description = t('description', { default: 'Detailed information about our comprehensive software development services.' });
  // const imageUrl = 'https://cslant.com/images/og-service-details.jpg';

  return {
    title: {
      default: title,
      template: '%s | CSlant',
    },
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: 'https://cslant.com/services/service-details',
      languages: {
        en: 'https://cslant.com/services/service-details',
        vi: 'https://cslant.com/vi/services/service-details',
      },
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'article',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

  };
}

export default function Page() {
  return (
    <>
      <Navbar />

      <PageBanner pageTitle="Services Style Five" />

      <ServiceDetailsContent />

      <Footer />
    </>
  );
};
