export interface IContact {
  full_name: string;
  user_email: string;
  content: string;
}

export interface IErrorField {
  errorContent: string;
}

export interface ICountContent {
  characterCount: number;
}

export type ErrorsProps = {
  full_name: string[],
  user_email: string[],
  content: string[]
}

export type PostContactResultType = {
  error: boolean | ErrorsProps,
  data: IContact,
  message: string
}
