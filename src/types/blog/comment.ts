export type TCommentItem = {
  id: number;
  author?: TAuthorComment;
  content: string;
  status?: {
    value: string;
    label: string;
  };
  is_liked: boolean;
  likes_count: string;
  website?: string;
  replies?: TCommentItem[];
};

export type TAuthorComment = {
  first_name: string;
  full_name: string;
  id: number;
  image: string;
  last_name: string;
  role: string | null;
};

export interface ICommentResponse {
  data: TCommentItem[];
  meta?: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  error?: boolean;
  message?: string;
}



export type CommentParams = {
  reference_id: string | number;
  page: string | number;
  per_page: string | number;
  order_by?: string;
  order?: 'asc' | 'desc';
};
