import { JSX, ReactNode } from "react";

// Interface in page for Language
export type Lang = "vi" | "en";

export type LocaleType = "vi_VN" | "en_US";

// Interface in page for submenu children header
export interface SubMenu {
  title?: string;
  url?: string;
}

// Interface in page for submenu header
export interface PropsMenu {
  sub?: Array<SubMenu>;
  title?: string;
  type?: string;
  url?: string;
  length?: number;
  map(arg0: (value: PropsMenu, index: number) => JSX.Element | null): ReactNode;
}

export const PAGE_SIZE_DEFAULT = 10;
export const PAGE_SIZE_24 = 24;
export const PAGE_SIZE_12 = 12;
export const PAGE_SIZE_6 = 6;
export const PAGE_SIZE_4 = 4;
