import createMiddleware from "next-intl/middleware";
import { NextRequest, NextResponse } from "next/server";
import { userAgent } from "next/server";
import { locales, defaultLocale } from "./i18n";

const i18nMiddleware = createMiddleware({
  // A list of all locales that are supported
  locales,

  // Used when no locale matches
  defaultLocale,

  // Never use locale prefix for default locale, but allow explicit locale paths
  localePrefix: 'as-needed',

  // Disable automatic locale detection
  localeDetection: false
});

const checkDeviceMiddleware = (
  request: NextRequest,
  response: NextResponse,
) => {
  // Check a device
  const { device } = userAgent(request);
  const isMobile = device.type === 'mobile';

  // Add a device type to request
  response.headers.set("x-device-type", isMobile ? "mobile" : "desktop");

  return response;
};

export default async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Get locale from cookie or use default
  const cookieLocale = request.cookies.get("cslant_locale")?.value ?? defaultLocale;

  // Determine locale based on URL path
  let locale: string = defaultLocale;

  // Check if URL starts with a locale (e.g., /en/about, /vi/contact)
  const pathnameHasLocale = locales.some(
    (loc) => pathname.startsWith(`/${loc}/`) || pathname === `/${loc}`
  );

  if (pathnameHasLocale) {
    // Extract locale from URL (e.g., /en/about -> en)
    locale = pathname.split('/')[1] as typeof locales[number];
  } else {
    // For paths without a locale prefix (e.g., /about, /contact), use cookie locale
    locale = cookieLocale as typeof locales[number];
    if (!locales.includes(locale as any)) {
      locale = defaultLocale;
    }
  }

  // Create request with custom headers for i18n
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-locale', locale);
  requestHeaders.set('x-pathname', pathname);

  const modifiedRequest = new NextRequest(request.url, {
    headers: requestHeaders,
  });

  let response = i18nMiddleware(modifiedRequest);

  // Set locale cookie
  response.cookies.set({
    name: "cslant_locale",
    value: locale,
    path: "/",
    httpOnly: false,
    domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN,
  });

  // Set headers for the response
  response.headers.set('x-locale', locale);
  response.headers.set('x-pathname', pathname);

  // Other middleware here
  response = checkDeviceMiddleware(request, response);
  return response;
}

export const config = {
  matcher: [
    // Match all paths except static files and API routes
    "/((?!_next|api|.*\\..*|favicon.ico).*)",
  ],
};
