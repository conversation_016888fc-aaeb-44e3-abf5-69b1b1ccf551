export interface PricingFeature {
  feature: string;
  isActive: boolean;
}

export interface PricingPlan {
  id: number;
  title: string;
  price: string;
  duration: string;
  features: PricingFeature[];
  isActive: boolean;
  delay: number;
}

export const pricingPlansData: PricingPlan[] = [
  {
    id: 1,
    title: "Basic Package",
    price: "100.00",
    duration: "",
    features: [
      { feature: "7 HTML Pages", isActive: true },
      { feature: "1 Main Page", isActive: true },
      { feature: "CMS Version Available", isActive: true },
      { feature: "Logo Design Included", isActive: true },
      { feature: "2 Revisions Available", isActive: true },
      { feature: "Available Support", isActive: true },
      { feature: "Source Code Included", isActive: true },
      { feature: "Single Domain License", isActive: true },
    ],
    isActive: false,
    delay: 100,
  },
  {
    id: 2,
    title: "Advanced Package",
    price: "180.00",
    duration: "",
    features: [
      { feature: "15 HTML Pages", isActive: true },
      { feature: "3 Main Pages", isActive: true },
      { feature: "CMS Version Available", isActive: true },
      { feature: "Logo Design Included", isActive: true },
      { feature: "2 Revisions Available", isActive: true },
      { feature: "Available Support", isActive: true },
      { feature: "Source Code Included", isActive: true },
      { feature: "Single Domain License", isActive: true },
    ],
    isActive: true,
    delay: 200,
  },
  {
    id: 3,
    title: "Platinum Package",
    price: "350.00",
    duration: "",
    features: [
      { feature: "25 HTML Pages", isActive: true },
      { feature: "5 Main Pages", isActive: true },
      { feature: "CMS Version Available", isActive: true },
      { feature: "Logo Design Included", isActive: true },
      { feature: "2 Revisions Available", isActive: true },
      { feature: "Available Support", isActive: true },
      { feature: "Source Code Included", isActive: true },
      { feature: "Single Domain License", isActive: true },
    ],
    isActive: false,
    delay: 300,
  },
  {
    id: 4,
    title: "E-Commerce",
    price: "700.00",
    duration: "",
    features: [
      { feature: "Shopping Cart with React", isActive: true },
      { feature: "Advanced Search Filters", isActive: true },
      { feature: "Single Provider Solution", isActive: true },
      { feature: "Logo Design Included", isActive: true },
      { feature: "2 Revisions Available", isActive: true },
      { feature: "Available Support", isActive: true },
      { feature: "Source Code Included", isActive: true },
      { feature: "Single Domain License", isActive: true },
    ],
    isActive: false,
    delay: 400,
  },
  {
    id: 5,
    title: "Advanced Platinum",
    price: "900.00",
    duration: "",
    features: [
      { feature: "50 HTML Pages", isActive: true },
      { feature: "10 Main Pages", isActive: true },
      { feature: "CMS Version Available", isActive: true },
      { feature: "Logo Design Included", isActive: true },
      { feature: "2 Revisions Available", isActive: true },
      { feature: "Available Support", isActive: true },
      { feature: "Source Code Included", isActive: true },
      { feature: "Single Domain License", isActive: true },
    ],
    isActive: false,
    delay: 500,
  },
  {
    id: 6,
    title: "Extended Platinum",
    price: "1000.00",
    duration: "",
    features: [
      { feature: "60 HTML Pages", isActive: true },
      { feature: "12 Main Pages", isActive: true },
      { feature: "CMS Version Available", isActive: true },
      { feature: "Logo Design Included", isActive: true },
      { feature: "2 Revisions Available", isActive: true },
      { feature: "Available Support", isActive: true },
      { feature: "Source Code Included", isActive: true },
      { feature: "Single Domain License", isActive: true },
    ],
    isActive: false,
    delay: 600,
  },
];
