import { fetcher } from '@/api';

export const categoryApi = {
  get: async <T = any>({
    params,
    slug = ''
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    slug?: string;
    type?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = slug ? `categories/${slug}` : 'categories';
    return fetcher<T>({ endpoint: path, params });
  }
};
