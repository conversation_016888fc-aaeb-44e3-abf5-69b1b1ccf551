import { fetcher } from '@/api';
import {
  ICommentResponse,
  CommentParams
} from '@/types/blog/comment';

export const commentApi = {
  getByPost: async (
    referenceId: string | number,
    page: number = 1,
    perPage: number = 10
  ): Promise<ICommentResponse> => {
    try {
      const params: CommentParams = {
        reference_id: referenceId,
        page,
        per_page: perPage,
        order_by: 'created_at',
        order: 'asc'
      };

      const response = await fetcher<ICommentResponse>({
        endpoint: 'interaction/comments/filters',
        params,
        type: 'blog'
      });

      return {
        data: response.data?.data || [],
        meta: response.meta,
        error: response.error ? true : false,
        message: response.error?.message
      };
    } catch (error) {
      return {
        data: [],
        error: true,
        message: 'Failed to fetch comments'
      };
    }
  }
};
