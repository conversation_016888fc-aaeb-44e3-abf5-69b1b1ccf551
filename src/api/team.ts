import { assets } from "@/utils/helper";

export const teamData = [
  {
    name: "<PERSON>",
    position: "CEO & Founder",
    image: assets('v2/images/team-image/tan-nguyen.png'),
    description:
      "Passionate about building scalable digital solutions and empowering businesses through technology.",
    socialLinks: [
      {
        url: "https://www.facebook.com/tanhongit",
        type: "facebook",
      },
      {
        url: "https://www.twitter.com/tanhongit",
        type: "twitter",
      },
      {
        url: "https://linkedin.com/in/tanhongit",
        type: "linkedin",
      },
      {
        url: "https://www.github.com/tanhongit",
        type: "github",
      },
    ],
  },
  {
    name: "<PERSON><PERSON><PERSON> (Alice)",
    position: "Key Account Management",
    image: assets('v2/images/team-image/tuyen-tong-min.png'),
    description:
      "Partnering with startups and enterprises to deliver products and services tailored to their needs, goals, and budget.",
    sub_description: "Partnering with technology clients, from local startups to global enterprises, guide them toward the ideal products and services that align perfectly with their unique needs, scope, and budget.",
    socialLinks: [
      {
        url: "https://www.facebook.com/tong.thanh.tuyen.529384",
        type: "facebook",
      },
    ],
  },
  {
    name: "My Truong (Maia)",
    position: "Technical Director",
    image: assets('v2/images/team-image/my-truong-maia-min.jpeg'),
    description:
      "Experienced in solving critical challenges with AI integration and advanced technology strategies.",
    socialLinks: [
      {
        url: "https://www.facebook.com/ngocmytruong.truong",
        type: "facebook",
      },
      {
        url: "https://linkedin.com/in/trgngocmy",
        type: "linkedin",
      },
      {
        url: "https://github.com/mytruong-z",
        type: "github",
      },
    ],
  },
  {
    name: "Loan Ngo",
    position: "Sr Software Engineer",
    image: assets('v2/images/team-image/bunhere-min.png'),
    description:
      "Expert software engineer delivering innovative, problem-solving solutions tailored to complex business domains.",
    socialLinks: [
      {
        url: "https://www.facebook.com/im.bunhere",
        type: "facebook",
      },
      {
        url: "https://bunhere.com",
        type: "website",
      },
      {
        url: "https://linkedin.com/in/bunhere",
        type: "linkedin",
      },
      {
        url: "https://github.com/bunheree",
        type: "github",
      },
    ],
  },

  {
    name: "Sen Doan",
    position: "Sr Fullstack Engineer",
    image: assets('v2/images/team-image/sendoan-min.png'),
    description:
        "Expert full-stack with specialization in building scalable web applications and optimizing user experiences.",
    socialLinks: [
      {
        url: "mailto:<EMAIL>",
        type: "email",
      },
      {
        url: "https://sendoan.com",
        type: "website",
      },
      {
        url: "https://linkedin.com/in/sendoan",
        type: "linkedin",
      },
      {
        url: "https://github.com/SenDTT",
        type: "github",
      },
    ],
  },
  {
    name: "Thuan Tran",
    position: "Frontend Developer",
    image: assets('v2/images/team-image/thuan-tran.png'),
    description:
      "Specialized in optimizing UX/UI and developing flexible, efficient interfaces across multiple platforms.",
    socialLinks: [
      {
        url: "https://www.facebook.com/thuan.tran.15219",
        type: "facebook",
      },
      {
        url: "https://x.com/ThuanTran15219",
        type: "twitter",
      },
      {
        url: "https://linkedin.com/in/thuan15219",
        type: "linkedin",
      },
      {
        url: "https://github.com/thuankg1752",
        type: "github",
      },
    ],
  },
  {
    name: "Tri Ho",
    position: "Backend Engineer",
    image: assets('v2/images/team-image/tri-minh-min.jpeg'),
    description:
      "Experienced in backend development, API design, cloud computing, and database management.",
    socialLinks: [
      {
        url: "https://github.com/hokhacminhtri",
        type: "github",
      },
    ],
  },
  {
    name: "Francis Duong",
    position: "Backend Developer",
    image: assets('v2/images/team-image/hao-duong-min.png'),
    description:
      "I love programming for turning ideas into reality and pushing my thinking further.",
    socialLinks: [
      {
        url: "https://www.facebook.com/duonganh.hao.96",
        type: "facebook",
      },
      {
        url: "https://x.com/DngAnhHo7",
        type: "twitter",
      },
      {
        url: "https://linkedin.com/in/francis-duong-8817b6265",
        type: "linkedin",
      },
      {
        url: "https://github.com/TonyDuong0509",
        type: "github",
      },
    ],
  },
  {
    name: "Thinh Pham",
    position: "PHP Developer",
    image: assets('v2/images/team-image/thinh-pham-min.png'),
    description:
      "Focused on building scalable web applications and delivering clean, efficient backend solutions.",
    socialLinks: [
      {
        url: "https://www.facebook.com/xuans.thinhs",
        type: "facebook",
      },
      {
        url: "https://linkedin.com/in/phamxuanthinh",
        type: "linkedin",
      },
      {
        url: "https://github.com/pxthinh",
        type: "github",
      },
    ],
  },
];
