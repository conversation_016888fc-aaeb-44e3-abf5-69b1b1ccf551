export const faqData = [
  {
    id: "cat_services",
    title: "Services",
    items: [
      {
        id: "s1",
        question: "What services does CSlant provide?",
        answer:
          "CSlant offers website and mobile app development, internal system solutions, UI/UX design, API integration, and tech project outsourcing.",
      },
      {
        id: "s2",
        question: "Can you help with MVPs or startup product ideas?",
        answer:
          "Absolutely. We frequently help startups build MVPs, landing pages, and validate product ideas with lean development approaches.",
      },
      {
        id: "s3",
        question: "Can you build internal business tools or custom systems?",
        answer:
          "Yes. We specialize in building CRMs, dashboards, reporting systems, and tailored solutions for small to mid-sized businesses.",
      },
      {
        id: "s4",
        question: "Do you provide design services too?",
        answer:
          "Yes. We offer UI/UX design as a standalone service or bundled into development projects, tailored to your brand and users.",
      },
    ],
  },
  {
    id: "cat_pricing",
    title: "Pricing & Process",
    items: [
      {
        id: "p1",
        question: "How much does it cost to build a website or application?",
        answer:
          "Our pricing varies based on the scope. Starter web packages begin at $120. Custom apps or systems are quoted per project requirements.",
      },
      {
        id: "p2",
        question: "What’s your development process like?",
        answer:
          "We follow a step-by-step agile workflow: Discovery → UI/UX → Development → Testing → Launch → Support.",
      },
      {
        id: "p3",
        question: "How long does a typical project take?",
        answer:
          "Simple websites take 1–2 weeks. More complex apps or systems may take 4–10 weeks, depending on features and scope.",
      },
      {
        id: "p4",
        question: "What if I need something that’s not listed in your packages?",
        answer:
          "No problem! Just send us your requirements, and we’ll create a custom solution and quote tailored to your project.",
      },
    ],
  },
  {
    id: "cat_tech",
    title: "Tech & Delivery",
    items: [
      {
        id: "t1",
        question: "What technologies do you use?",
        answer:
          "Our core tech stack includes PHP, Laravel, Wordpress, Yii, Node.js, React, Next.js, Remix.js, Ruby on Rails, PostgreSQL, MySQL, and other modern tools depending on the project.",
      },
      {
        id: "t2",
        question: "Do I own the source code after the project is done?",
        answer:
          "Yes, once the final payment is completed, you receive full access and ownership of the source code and deployment assets.",
      },
      {
        id: "t3",
        question: "Do you provide post-launch support or maintenance?",
        answer:
          "Yes. All packages include post-launch support (1–3 months). You can also subscribe to extended maintenance plans if needed.",
      },
      {
        id: "t4",
        question: "Can you work on an existing website or system?",
        answer:
          "Yes, we can audit, update, or improve existing platforms — assuming access and tech compatibility are available.",
      },
      {
        id: "t5",
        question: "Can you integrate third-party services like payment gateways or CRMs?",
        answer:
          "Yes, we frequently integrate services like Stripe, VNPay, Firebase, Mailchimp, Hubspot, and more based on your needs.",
      },
    ],
  },
  {
    id: "cat_general",
    title: "General",
    items: [
      {
        id: "g1",
        question: "Can I work with CSlant remotely?",
        answer:
          "Yes, we work fully online with clients worldwide. Communication is streamlined using email, chat, and regular progress calls.",
      },
      {
        id: "g2",
        question: "Do you offer SEO optimization?",
        answer:
          "Yes. Basic SEO is included in most packages, and we also offer technical SEO setups as an add-on service.",
      },
      {
        id: "g3",
        question: "What payment methods do you accept?",
        answer:
          "We accept bank transfers, PayPal, and flexible arrangements for international clients. Payment schedules depend on project size.",
      },
    ],
  },
  {
    id: "cat_automation",
    title: "AI & Automation",
    items: [
      {
        id: "ai1",
        question: "Can CSlant help automate business processes using AI?",
        answer:
          "Yes. We design custom automation workflows using AI and third-party tools to streamline repetitive tasks, reduce errors, and improve efficiency.",
      },
      {
        id: "ai2",
        question: "Do you build AI-based features into apps or systems?",
        answer:
          "Absolutely. We can integrate features like smart search, auto-recommendation, chatbots, and intelligent analytics using modern AI libraries and APIs.",
      },
      {
        id: "ai3",
        question: "Can automation really help reduce business costs?",
        answer:
          "Yes. Automating manual workflows and decision-making processes with smart logic or AI can significantly reduce operational overhead and improve ROI.",
      },
      {
        id: "ai4",
        question: "Do you consult on workflow optimization and tech adoption?",
        answer:
          "Yes. We offer consultation to help you identify pain points in your workflow and recommend cost-effective digital solutions and automation strategies.",
      },
    ],
  },
];
