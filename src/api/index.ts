import { BLOG_API_URL, MAIN_API_URL } from '@/utils/env';

export type FetchOptions<T = any> = {
  endpoint: string;
  type?: string;
  params?: Record<string, any>;
  headers?: HeadersInit;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: BodyInit | null;
  next?: {revalidate: number};
};

const handleSwitchEndpoint = (typeRequest: string) => {
  switch (typeRequest) {
    case 'home':
      return MAIN_API_URL;
    case 'blog':
    default :
      return BLOG_API_URL;
  }
};

function buildUrlWithParams(data: {params?: Record<string, any>; type: string; endpoint: string;}): string {
  const { params, type, endpoint } = data;
  const url = `${handleSwitchEndpoint(type)}/${endpoint}`;
  if (!params) {
    return url;
  }
  const query = new URLSearchParams();
  for (const key in params) {
    if (params[key] !== undefined && params[key] !== null) {
      query.append(key, String(params[key]));
    }
  }
  return url.includes('?') ? `${url}&${query}` : `${url}?${query}`;
}

export async function fetcher<T = any>({
  endpoint,
  type = 'blog',
  params,
  headers = {},
  method = 'GET',
  body = null,
  next
}: FetchOptions): Promise<{data: T | null; error: any; meta: any}> {
  const url = buildUrlWithParams({
    params,
    type,
    endpoint
  });
  try {
    const normalizedHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      ...( headers instanceof Headers ? Object.fromEntries(headers.entries()) : headers ),
      typeEndpoint: type ?? 'blog'
    };

    const res = await fetch(url, {
      method,
      headers: normalizedHeaders,
      body,
      ...( method === 'GET' ? { next: { ...next, revalidate: 60 } } : {} ),
      credentials: 'include'
    });

    const json = await res.json();
    if (!res.ok) {

      return { data: null, error: { message: await res.text() }, meta: null };
    }

    return { data: json?.data ?? null, meta: json?.meta ?? null, error: null };
  } catch (error) {
    return { data: null, error, meta: null };
  }
}
